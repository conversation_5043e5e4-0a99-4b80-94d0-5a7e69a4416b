/** app/sitemap.xml/route.ts **/
export const dynamic = "force-static";

function getSitemap() {
  const map = [
    {
      url: "https://myvethub.com/",
      lastModified: new Date(),
      changeFrequency: "weekly",
      priority: 1,
    },
    {
      url: "https://myvethub.com/product/marketing/",
      lastModified: new Date(),
      changeFrequency: "weekly",
      priority: 0.8,
    },
    {
      url: "https://myvethub.com/product/phones/",
      lastModified: new Date(),
      changeFrequency: "weekly",
      priority: 0.8,
    },
    {
      url: "https://myvethub.com/product/communication/",
      lastModified: new Date(),
      changeFrequency: "weekly",
      priority: 0.8,
    },
    {
      url: "https://myvethub.com/product/appointment-manager/",
      lastModified: new Date(),
      changeFrequency: "weekly",
      priority: 0.8,
    },
    {
      url: "https://myvethub.com/blog/",
      lastModified: new Date(),
      changeFrequency: "weekly",
      priority: 0.6,
    },
    {
      url: "https://myvethub.com/contact/",
      lastModified: new Date(),
      changeFrequency: "monthly",
      priority: 0.5,
    },
  ];

  return `<?xml version="1.0" encoding="UTF-8"?>
    <urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
        ${map
          .map(
            (item) => `
                <url>
                  <loc>${item.url}</loc>
                  <lastmod>${
                    item.lastModified.toISOString().split("T")[0]
                  }</lastmod>
                  <changefreq>${item.changeFrequency}</changefreq>
                  <priority>${item.priority}</priority>
                </url>
              `
          )
          .join("")}
        </urlset>
      `;
}

export async function GET() {
  return new Response(getSitemap(), {
    headers: {
      "Content-Type": "text/xml",
    },
  });
}
