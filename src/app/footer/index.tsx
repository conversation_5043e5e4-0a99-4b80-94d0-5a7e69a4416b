"use client";
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, MapPin, Phone } from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import { useEffect, useState } from "react";

const Footer = () => {
  const [isVisible, setIsVisible] = useState(false);
  const currentYear = new Date().getFullYear();

  useEffect(() => {
    const handleScroll = () => {
      if (window.scrollY > 100) {
        setIsVisible(true);
      } else {
        setIsVisible(false);
      }
    };

    window.addEventListener("scroll", handleScroll);

    return () => {
      window.removeEventListener("scroll", handleScroll);
    };
  }, []);

  const scrollToTop = () => {
    return (
      <a
        href="#"
        onClick={(e) => {
          e.preventDefault();
          window.scrollTo({
            top: 0,
            behavior: "smooth", // Smooth scrolling
          });
        }}
      >
        <button className="fixed shadow-md bottom-28 right-5 flex items-center justify-center w-11 h-11 border rounded-full border-white bg-secondary text-white cursor-pointer group">
          <div
            title="Scroll To Top"
            className="transform translate-y-0 transition-transform duration-700 group-hover:-translate-y-1"
          >
            <ChevronUp size={22} className="font-extrabold" />
          </div>
        </button>
      </a>
    );
  };

  return (
    <>
      <footer className="w-full mx-auto py-12 px-4 bg-secondary text-white">
        <div className="container m-auto grid sm:grid-cols-2 md:grid-cols-4 md:gap-6  gap-y-12 sm:gap-y-6 pb-10">
          <div className="col-span-full lg:col-span-1 pb-2">
            <Image
              src="/images/myvethub_white.png"
              alt="logo"
              width={0}
              height={0}
              className="object-contain w-32 h-auto"
              priority
            />
            {/* for availble for android and ios */}
            {/* <div className="flex items-center mt-6 gap-3">
              <a
                href="https://play.google.com/store/apps/details?id=com.example" // Replace with your app's Play Store link
                target="_blank"
                rel="noopener noreferrer"
                className="bg-black text-white px-4 py-2 rounded-md flex items-center gap-2 transition duration-300"
              >
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 26 26">
                  <path
                    fill="white"
                    fillRule="evenodd"
                    d="M 24.121094 9.753906 L 24.121094 16.035156 C 24.121094 16.921875 23.414063 17.640625 22.539063 17.640625 C 21.667969 17.640625 20.960938 16.921875 20.960938 16.035156 L 20.960938 9.753906 C 20.960938 8.867188 21.664063 8.148438 22.539063 8.148438 C 23.414063 8.148438 24.121094 8.867188 24.121094 9.753906 Z M 3.457031 8.148438 C 2.585938 8.148438 1.878906 8.867188 1.878906 9.753906 L 1.878906 16.03125 C 1.878906 16.917969 2.585938 17.640625 3.457031 17.640625 C 4.335938 17.640625 5.039063 16.917969 5.039063 16.03125 L 5.039063 9.753906 C 5.039063 8.867188 4.335938 8.148438 3.457031 8.148438 Z M 5.992188 19.664063 C 5.992188 20.347656 6.546875 20.898438 7.226563 20.898438 L 8.636719 20.898438 L 8.636719 24.394531 C 8.636719 25.28125 9.347656 26 10.21875 26 C 11.09375 26 11.800781 25.28125 11.800781 24.394531 L 11.800781 20.898438 L 14.261719 20.898438 L 14.261719 24.394531 C 14.261719 25.28125 14.972656 26 15.84375 26 C 16.71875 26 17.425781 25.28125 17.425781 24.394531 L 17.425781 20.898438 L 18.835938 20.898438 C 19.515625 20.898438 20.070313 20.351563 20.070313 19.667969 L 20.070313 8.195313 L 5.992188 8.195313 Z M 20.035156 7.175781 L 5.960938 7.175781 C 6.144531 5.039063 7.59375 3.214844 9.664063 2.253906 L 8.363281 0.363281 C 8.285156 0.253906 8.304688 0.105469 8.402344 0.0351563 C 8.503906 -0.03125 8.648438 0 8.730469 0.113281 L 10.078125 2.074219 C 10.96875 1.722656 11.957031 1.527344 13 1.527344 C 14.042969 1.527344 15.027344 1.722656 15.921875 2.074219 L 17.269531 0.113281 C 17.351563 0 17.496094 -0.03125 17.597656 0.0351563 C 17.695313 0.105469 17.714844 0.253906 17.636719 0.363281 L 16.335938 2.253906 C 18.402344 3.214844 19.855469 5.039063 20.035156 7.175781 Z M 10.8125 4.445313 C 10.8125 4.015625 10.460938 3.671875 10.03125 3.671875 C 9.601563 3.671875 9.25 4.019531 9.25 4.445313 C 9.25 4.878906 9.601563 5.226563 10.03125 5.226563 C 10.460938 5.226563 10.8125 4.878906 10.8125 4.445313 Z M 16.84375 4.445313 C 16.84375 4.015625 16.496094 3.671875 16.066406 3.671875 C 15.636719 3.671875 15.289063 4.019531 15.289063 4.445313 C 15.289063 4.878906 15.636719 5.226563 16.066406 5.226563 C 16.496094 5.226563 16.84375 4.878906 16.84375 4.445313 Z"
                  />
                </svg>

                <span className="font-semibold">Android</span>
              </a>
              <a
                href="https://apps.apple.com/app/id0000000000" // Replace with your app's App Store link
                target="_blank"
                rel="noopener noreferrer"
                className="bg-black text-white px-4 py-2 rounded-md flex items-center gap-2  transition duration-300"
              >
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" fill="white" viewBox="0 0 24 24">
                  <path d="M16.41 13.75c-.02-2.12 1.74-3.13 1.82-3.17-1-1.47-2.55-1.67-3.1-1.7-1.3-.13-2.54.76-3.2.76-.66 0-1.69-.74-2.78-.72-1.42.02-2.74.83-3.47 2.1-1.48 2.57-.38 6.37 1.05 8.46.7 1.02 1.53 2.15 2.63 2.11 1.06-.04 1.46-.68 2.75-.68 1.28 0 1.65.68 2.78.66 1.15-.02 1.88-1.04 2.57-2.06a9.3 9.3 0 001.1-2.25c-2.89-1.1-2.73-3.24-2.73-3.25zM14.85 4.55c.58-.71.98-1.7.88-2.7-.85.03-1.88.56-2.49 1.26-.55.62-1.02 1.63-.89 2.59.94.07 1.92-.48 2.5-1.15z" />
                </svg>
                <span className="font-semibold">iOS</span>
              </a>
            </div> */}
          </div>
          {/* <div>
            <h4 className="text-md font-semibold text-white uppercase ">
              Company
            </h4>

            <div className="mt-3 grid space-y-3 text-sm">
              <p>
                <Link
                  className="inline-flex gap-x-2 text-white hover:font-medium focus:outline-hidden focus:text-cyan-200  "
                  href="/"
                >
                  About us
                </Link>
              </p>
            </div>
          </div> */}

          <div>
            <h4 className="text-md font-semibold text-white uppercase ">
              Product
            </h4>

            <div className="mt-3 grid space-y-3 text-sm">
              <p>
                <Link
                  className="inline-flex gap-x-2 text-white hover:font-medium focus:outline-hidden focus:text-cyan-200  "
                  href="/product/marketing/"
                >
                  {/* Digital Presence */}
                  Website Plus
                </Link>
              </p>
              <p>
                <Link
                  className="inline-flex gap-x-2 text-white hover:font-medium focus:outline-hidden focus:text-cyan-200  "
                  href="/product/phones/"
                >
                  Intelligent Telephony
                </Link>
              </p>
              <p>
                <Link
                  className="inline-flex gap-x-2 text-white hover:font-medium focus:outline-hidden focus:text-cyan-200  "
                  href="/product/inventory"
                >
                  {/* Supplies Tracker */}
                  Smart Inventory
                </Link>
              </p>
              <p>
                <Link
                  className="inline-flex gap-x-2 text-white hover:font-medium focus:outline-hidden focus:text-cyan-200  "
                  href="/product/telemedicine"
                >
                  {/* Virtual Care */}
                  Telemedicine
                </Link>
              </p>
              <p>
                <Link
                  className="inline-flex gap-x-2 text-white hover:font-medium focus:outline-hidden focus:text-cyan-200  "
                  href="/product/appointment-manager/"
                >
                  Appointment Manager
                </Link>
              </p>
              <p>
                <Link
                  className="inline-flex gap-x-2 text-white hover:font-medium focus:outline-hidden focus:text-cyan-200  "
                  href="/product/communication/"
                >
                  Communication
                </Link>
              </p>
              <p>
                <Link
                  className="inline-flex gap-x-2 text-white hover:font-medium focus:outline-hidden focus:text-cyan-200  "
                  href="/forms/"
                >
                  Digital Forms
                </Link>
              </p>
                 <p>
                <Link
                  className="inline-flex gap-x-2 text-white hover:font-medium focus:outline-hidden focus:text-cyan-200  "
                  href="/ai-transcribe/"
                >
                 AI Transcribe
                </Link>
              </p>
            </div>
          </div>

          <div>
            <h4 className="text-md font-semibold text-white uppercase ">
              Resources
            </h4>

            <div className="mt-3 grid space-y-3 text-sm">
              <p>
                <a
                  className="inline-flex gap-x-2 text-white hover:font-medium focus:outline-hidden focus:text-cyan-200  "
                  href="/faqs/"
                >
                  FAQ
                </a>
              </p>

              {/* <p>
                <a 
                className="inline-flex gap-x-2 text-white hover:font-medium focus:outline-hidden focus:text-cyan-200"
                href="/forms/"
                >
                  Forms
                </a>
              </p> */}

              <p>
                <Link
                  className="inline-flex gap-x-2 text-white hover:font-medium focus:outline-hidden focus:text-cyan-200  "
                  href="/blog/"
                >
                  Blogs
                </Link>
              </p>
              {/* <p>
                <Link
                  className="inline-flex gap-x-2 text-white hover:font-medium focus:outline-hidden focus:text-cyan-200  "
                  href="/privacy-policy/"
                >
                  Privacy-Policy
                </Link>
              </p>
              <p>
                <Link
                  className="inline-flex gap-x-2 text-white hover:font-medium focus:outline-hidden focus:text-cyan-200  "
                  href="/terms-of-use/"
                >
                  Terms-of-Use
                </Link>
              </p>
              <p>
                <Link
                  className="inline-flex gap-x-2 text-white hover:font-medium focus:outline-hidden focus:text-cyan-200  "
                  href="/delete-account/"
                >
                  Delete Account
                </Link>
              </p>
              <p>
                <Link
                  className="inline-flex gap-x-2 text-white hover:font-medium focus:outline-hidden focus:text-cyan-200  "
                  href="/eula/"
                >
                  EULA
                </Link>
              </p> */}
              {/* <p>
                <a
                  className="inline-flex gap-x-2 text-white hover:font-medium focus:outline-hidden focus:text-cyan-200  "
                  href="#"
                >
                  FAQs
                </a>
              </p> */}
            </div>
          </div>

          <div>
            <h4 className="text-md font-semibold text-white uppercase ">
              Contact
            </h4>

            <div className="mt-3 grid space-y-3">
              <ul className="footer-information text-white">
                <li className="flex items-center">
                  <span className="flex items-center">
                    <MapPin size={18} className="mr-2" />
                  </span>
                  <span>38930 Blacow Rd, Ste B3, Fremont, CA 94536 USA</span>
                </li>
                <li className="flex items-center py-3">
                  <a href="#" className="flex items-center pointer-events-none">
                    <MailCheck size={18} className="mr-2" />
                    <span><EMAIL></span>
                  </a>
                </li>
                <li className="flex items-center">
                  <a href="tel:****** 372 5811" className="flex items-center">
                    {" "}
                    <Phone size={18} className="mr-2" />
                    <span>****** 372 5811</span>
                  </a>
                </li>
              </ul>
            </div>
          </div>
        </div>
        <div className="container m-auto py-6 border-t border-gray-300 ">
          <div className="flex flex-wrap justify-start items-center gap-3 md:gap-5 text-sm pb-5">
            <p>
              <Link
                className="inline-flex hover:underline gap-x-2 text-white hover:font-medium focus:outline-hidden focus:text-cyan-200 "
                href="/privacy-policy/"
              >
                Privacy Policy
              </Link>
            </p>
             <p>
              <Link
                className="inline-flex hover:underline gap-x-2 text-white hover:font-medium focus:outline-hidden focus:text-cyan-200 "
                href="/cookie-policy/"
              >
                Cookie Policy
              </Link>
            </p>
            <p>
              <Link
                className="inline-flex hover:underline gap-x-2 text-white hover:font-medium focus:outline-hidden focus:text-cyan-200"
                href="/terms-of-use/"
              >
                Terms of Use
              </Link>
            </p>
            {/* <p>
              <Link
                className="inline-flex hover:underline gap-x-2 text-white hover:font-medium focus:outline-hidden focus:text-cyan-200  "
                href="/eula/"
              >
                EULA
              </Link>
            </p>
            <p>
              <Link
                className="inline-flex hover:underline gap-x-2 text-white hover:font-medium focus:outline-hidden focus:text-cyan-200  "
                href="/delete-account/"
              >
                Delete Account
              </Link>
            </p> */}
          </div>
          <div className="flex flex-wrap justify-start items-center gap-2">
            <div>
              <p className="text-sm text-white ">© {currentYear} MyVetHub</p>
            </div>
          </div>
        </div>
      </footer>

      {isVisible && scrollToTop()}
    </>
  );
};

export default Footer;
