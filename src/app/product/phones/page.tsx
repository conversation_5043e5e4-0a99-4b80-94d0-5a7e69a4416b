import Image from "next/image";

const Phones = () => {
  return (
    <div>
      <div className="header">
        <div className="relative isolate py-24 sm:py-42 overflow-hidden">
          <div className="container mx-auto px-4 sm:px-8">
            <div className="absolute inset-0 -z-20 flex justify-end">
              <div className="relative w-1/2 h-full">
                <Image
                  src="/images/telephone.avif"
                  alt=""
                  className="absolute inset-0 h-full w-full object-cover object-center"
                  width={0}
                  height={0}
                  priority
                />
              </div>
            </div>

            <div className="absolute inset-0 -z-10 bg-gradient-to-r from-white via-sky-100 to-transparent"></div>

            <div className="">
              <div className="mx-auto lg:max-w-3xl lg:mx-0">
                <h2 className="text-3xl md:text-4xl font-medium">
                  21st Century Telephony Solutions for Modern Veterinary Hospitals
                </h2>
                <p className="mt-6 text-lg leading-8 text-gray-700">
                  <span className="font-medium">MyVetHub</span> has taken the phone system to newer heights. We will not only help you to manage the client experience but will also give your team data and insights on where the overall communication stands. Our easy to use, unique system ushers in not-seen-before efficiency to the client communication. We improve revenue and customer retention; that is a win-win!
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="py-10 md:py-20 container mx-auto">
        <div className="container mx-auto px-4 sm:px-8 lg:grid lg:grid-cols-12 lg:gap-x-12 pb-5">
          <div className="lg:col-span-5">
            <div className="mb-16 lg:mb-0 xl:mt-16 text-black">
              <h2 className="mb-6 text-xl sm:text-2xl md:text-3xl">
                Our <span className="font-medium italic"> AI-Powered </span> Telephony Tools
              </h2>
              <p className="mb-6">
                Our cloud-based VoIP system isn’t just a phone — it’s a smart communication hub:
              </p>

              <ul className="pl-5 space-y-2 sm:space-y-4">
                {[
                  "Reliable, crystal-clear calling from the desk or from mobile app",
                  "Visibility into communication status",
                  "Integrated with telemedicine appointment system",
                  "Cost-effective with predictable monthly pricing",
                  "Automated SMS reminders"
                ].map((text, idx) => (
                  <li key={idx} className="flex gap-x-3 items-start">
                    {/* <span className="mt-1 size-5 flex justify-center items-center rounded-full bg-blue-50 text-blue-600">
                      <svg
                        className="shrink-0  p-1"
                        xmlns="http://www.w3.org/2000/svg"
                        width="24"
                        height="24"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                        strokeWidth="2"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                      >
                        <polyline points="20 6 9 17 4 12" />
                      </svg>
                    </span> */}
                    <span className="mt-2 w-2 h-2 bg-secondary rounded-full flex-shrink-0"></span>
                    <div className="grow text-sm sm:text-base font-semibold">
                      {text}
                    </div>
                  </li>
                ))}
              </ul>
              <p className="pt-10">
                Fast, friendly service starts the moment the phone rings
              </p>
            </div>
          </div>
          <div className="lg:col-span-7">
            <div className="xl:ml-14">
              {/* <Image
                className="inline sm:px-20"
                // src="/images/Chat_bot.png"
                src="/images/myvethub_voip.webp"
                alt="alternative"
                style={{ width: "auto", height: "auto" }}
                width={0}
                height={0}
              /> */}
              <div className="rounded-lg overflow-hidden w-full max-w-[600px] mx-auto">
                <Image
                  src="/images/myvethub_voip.webp"
                  alt="Website Example"
                  width={600}
                  height={400}
                  className="w-full h-auto object-contain rounded-lg"
                />
              </div>
            </div>
          </div>
        </div>

        {/* <div className="container mx-auto bg-gradient-to-l from-[#e4f2fe] to-[#fff3fe] rounded-[20px] px-4 sm:px-8  lg:grid lg:grid-cols-12 lg:gap-x-12">
          <div className="order-1 lg:order-2 lg:col-span-5">
            <div className="xl:mt-12 text-black">
              <h2 className="mb-6 text-xl sm:text-2xl md:text-3xl">
                NLP-Enabled IVR (Interactive Voice Response)
              </h2>

              <div className="mt-5 mb-10 lg:col-span-5">
                <div className="space-y-6 sm:space-y-8">
                  <div className="mb-6">
                    <p className="mb-2">
                      No more frustrating menus or endless hold times
                    </p>
                    <p>
                      Our{" "}
                      <span className="font-medium">
                        Natural Language Processing (NLP)-enabled IVR
                      </span>{" "}
                      lets pet owners simply speak their needs — and get routed
                      quickly to the right team member:
                    </p>
                  </div>

                  <ul className="pl-5 space-y-2 sm:space-y-4">
                    {[
                      "Understands natural speech, not just keypad inputs",
                      "Intelligent call flows based on common veterinary needs (appointments, emergencies, prescriptions)",
                      "Customizable scripts and responses to match your practice's voice",
                    ].map((text, idx) => (
                      <li key={idx} className="flex gap-x-3 items-start">
                       
                        <span className="mt-2 w-2 h-2 bg-secondary rounded-full flex-shrink-0"></span>
                        <div className="grow text-sm sm:text-base font-semibold">
                          {text}
                        </div>
                      </li>
                    ))}
                  </ul>

                  <p className="pt-2">
                    Fast, friendly service starts the moment the phone rings
                  </p>
                </div>
              </div>
            </div>
          </div>
          <div className="order-2 lg:order-1 lg:col-span-7">
            <div className="mb-12 lg:mb-0 xl:mr-14">
              <Image
                className="inline p-5 sm:px-14"
                src="/images/Voice_chat.png"
                alt="alternative"
                style={{ width: "auto", height: "auto" }}
                width={0}
                height={0}
              />
            </div>
          </div>
        </div> */}

        <div className="container my-15 mx-auto bg-gradient-to-l from-[#e4f2fe] to-[#fff3fe] rounded-[20px] px-4 sm:px-8  lg:grid lg:grid-cols-12 lg:gap-x-12">
          <div className="lg:col-span-5">
            <div className="pb-14 lg:mb-0 pt-16 text-black">
              <h2 className="mb-6 text-xl sm:text-2xl md:text-3xl">
                Call Transcription
              </h2>
              <p className="mb-6">Turn every call into actionable insight:</p>

              <ul className="pl-5 space-y-2 sm:space-y-4">
                {[
                  "Automatic call transcription for easy record-keeping and training",
                  "Identify trends, improve client service, and protect your staff from miscommunication",
                ].map((text, idx) => (
                  <li key={idx} className="flex gap-x-3 items-start">
                    {/* <span className="mt-1 size-5 flex justify-center items-center rounded-full bg-blue-50 text-blue-600">
                      <svg
                        className="shrink-0  p-1"
                        xmlns="http://www.w3.org/2000/svg"
                        width="24"
                        height="24"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                        strokeWidth="2"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                      >
                        <polyline points="20 6 9 17 4 12" />
                      </svg>
                    </span> */}
                    <span className="mt-2 w-2 h-2 bg-secondary rounded-full flex-shrink-0"></span>
                    <div className="grow text-sm sm:text-base font-semibold">
                      {text}
                    </div>
                  </li>
                ))}
              </ul>

              <p className="pt-10">
                Understand not just what’s said — but how it’s said
              </p>
            </div>
          </div>
          <div className="lg:col-span-7">
            <div className="md:ml-14">
              <Image
                className="inline sm:px-10"
                src="/images/Voice_control.png"
                alt="alternative"
                style={{ width: "auto", height: "auto" }}
                width={0}
                height={0}
              />
            </div>
          </div>
        </div>

        <section id="cta" className="">
          <div className="container mx-auto">
            <div className=" text-black md:rounded-[20px] p-4 md:p-0 lg:px-10 xl:px-16">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 items-center">
                {" "}
                {/* Added items-center */}
                {/* <div className="w-full px-4 flex justify-center">
                  <Image
                    // src="/images/Date_picker.png"
                    src={'/images/myvethub_appointment.webp'}
                    alt="image"
                    className="max-w-full h-auto p-3 sm:px-16 sm:w-2/3"
                    style={{ width: "auto", height: "auto" }}
                    width={0}
                    height={0}
                  />
                </div> */}
                {/* <div className="rounded-lg overflow-hidden w-full max-w-[450px] mx-auto">
                <Image
                  src="/images/myvethub_appointment.webp"
                  alt="Website Example"
                  width={600}
                  height={400}
                  className="w-full h-auto object-contain rounded-lg"
                />
              </div> */}
                <div className="rounded-lg overflow-hidden p-5 sm:px-14">
                  <Image
                    src="/images/myvethub_appointment.webp"
                    alt="alternative"
                    width={600}
                    height={400}
                    className="w-full h-auto object-contain rounded-lg"
                  />
                </div>
                <div className="w-full px-4 sm:px-8 flex md:justify-center">
                  <div className="py-8 sm:py-12">
                    <h2 className="text-xl sm:text-2xl md:text-3xl mb-7 ">
                      Smart Call Routing
                    </h2>

                    <p className="mb-6">
                      Our telephony system goes beyond basic call transfers:
                    </p>
                    <ul className="pl-5 space-y-2 sm:space-y-4">
                      {[
                        "Smart routing sends calls to the right department, doctor, or on-call technician",
                        "After-hours call handling ensures emergencies are always prioritized",
                      ].map((text, idx) => (
                        <li key={idx} className="flex gap-x-3 items-start">
                          {/* <span className="mt-1 size-5 flex justify-center items-center rounded-full bg-blue-50 text-blue-600">
                            <svg
                              className="shrink-0  p-1"
                              xmlns="http://www.w3.org/2000/svg"
                              width="24"
                              height="24"
                              viewBox="0 0 24 24"
                              fill="none"
                              stroke="currentColor"
                              strokeWidth="2"
                              strokeLinecap="round"
                              strokeLinejoin="round"
                            >
                              <polyline points="20 6 9 17 4 12" />
                            </svg>
                          </span> */}
                          <span className="mt-2 w-2 h-2 bg-secondary rounded-full flex-shrink-0"></span>
                          <div className="grow text-sm sm:text-base font-semibold">
                            {text}
                          </div>
                        </li>
                      ))}
                    </ul>
                    <p className="pt-10">
                      Your phone system should work as hard as your team does —
                      and now it can
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>
      </div>
    </div>
  );
};

export default Phones;
