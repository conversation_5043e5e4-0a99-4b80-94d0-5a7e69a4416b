"use client";
import Image from "next/image";

const AppointmentManager = () => {
  return (
    <div>
      <section className="header">
        <div className="relative isolate py-24 sm:py-42 overflow-hidden">
          <div className="container mx-auto px-4 sm:px-8">
            <div className="absolute inset-0 -z-20 flex justify-end">
              <div className="relative w-1/2 h-full">
                <Image
                  src="/images/vet_pratices.avif"
                  alt=""
                  className="absolute inset-0 h-full w-full object-cover object-center"
                  width={0}
                  height={0}
                  priority
                />
              </div>
            </div>

            <div className="absolute inset-0 -z-10 bg-gradient-to-r from-white via-blue-50 to-transparent"></div>

            <div className="">
              <div className="mx-auto lg:max-w-3xl lg:mx-0">
                <h2 className="text-3xl md:text-4xl font-medium">
                  Smart Scheduling for Veterinary Practices
                </h2>
                <p className="mt-6 text-lg leading-8 text-gray-700">
                  Effortless appointment management — happier clients, less
                  staff stress.
                </p>
                <p>
                  Our{" "}
                  <span className="font-medium">Smart Scheduling system</span>{" "}
                  is built to save your team time, reduce no-shows, and keep
                  your daily workflow running smoothly.
                </p>
              </div>
            </div>
          </div>
        </div>
      </section>

      <section className="py-10 md:py-20">
        <div className="w-full px-4 mx-auto text-center">
          <h2 className="font-medium mb-3 text-xl sm:text-2xl md:text-3xl text-black">
            Intelligent Tools That{" "}
            <span className="font-medium text-primary">
              Save Time and Boost Satisfaction
            </span>
          </h2>
          <p className="md:max-w-3xl mx-auto text-lg leading-8 text-gray-700">
            Smart Scheduling goes beyond a simple calendar. It’s a complete
            appointment management solution designed for the unique needs of
            veterinary hospitals and clinics.
          </p>
        </div>
        <div className="container mx-auto  px-4 pb-6 pt-10 gap-y-6 grid lg:grid-cols-2 gap-5">
          <div className="flex flex-col sm:flex-row gap-3 bg-[#E6F0FA] p-5 border border-gray-100 shadow-md rounded-2xl overflow-hidden hover:scale-[1.02] hover:shadow-md transition-all duration-300 border-l-4 border-l-[#0274D9]">
            <div className="w-full md:w-[30%]">
              <Image
                width={0}
                height={0}
                className=" w-full h-auto object-cover object-center transition duration-50"
                loading="lazy"
                src="/images/directBook.png"
                alt={""}
              />
            </div>

            <div className="w-full md:w-[70%] pl-4 text-black">
              <p className="text-lg md:text-xl font-medium">
                Integrated Calendar
              </p>

              <p className="pt-4 pb-3">
                Manage appointments, telemedicine consults, surgeries, and
                technician visits — all from a single, easy-to-navigate calendar
                view
              </p>
              {[
                "Color-coded appointment types",
                "Real-time updates across your team",
                "Drag-and-drop rescheduling",
              ].map((item) => (
                <li
                  key={item}
                  className="flex items-center gap-x-2 text-sm pb-1"
                >
                  {/* <span className="mt-1 size-5 flex justify-center items-center rounded-full bg-blue-50 text-blue-600">
                    <svg
                      className="shrink-0  p-1"
                      xmlns="http://www.w3.org/2000/svg"
                      width="24"
                      height="24"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    >
                      <polyline points="20 6 9 17 4 12" />
                    </svg>
                  </span> */}
                  <span className="mt-1 ml-5 w-2 h-2 bg-secondary rounded-full flex-shrink-0"></span>
                  <span className="font-semibold">{item}</span>
                </li>
              ))}
            </div>
          </div>
          <div className="flex flex-col sm:flex-row gap-3 bg-[#E6F0FA] p-5 border border-gray-100 shadow-md rounded-2xl overflow-hidden hover:scale-[1.02] hover:shadow-md transition-all duration-300 border-l-4 border-l-[#0274D9]">
            <div className="w-full md:w-[30%]">
              <Image
                width={0}
                height={0}
                className=" w-full h-auto object-cover object-center transition duration-50"
                loading="lazy"
                src="/images/smartMarketingTool.png"
                alt={""}
              />
            </div>
            <div className="w-full md:w-[70%] pl-4 text-black">
              <p className="text-lg md:text-xl font-medium">
                DVM-Specific Slot Management
              </p>

              <p className="pb-3 pt-4">
                Every veterinarian has a different schedule. With DVM-specific
                slot control, you can:
              </p>
              {[
                "Assign appointment types and lengths per doctor",
                "Reserve time for specialty consults or procedures",
                "Balance caseloads efficiently across your team",
              ].map((item) => (
                <li
                  key={item}
                  className="flex items-center gap-x-2 text-sm pb-1"
                >
                  {/* <span className="mt-1 size-5 flex justify-center items-center rounded-full bg-blue-50 text-blue-600">
                    <svg
                      className="shrink-0  p-1"
                      xmlns="http://www.w3.org/2000/svg"
                      width="24"
                      height="24"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    >
                      <polyline points="20 6 9 17 4 12" />
                    </svg>
                  </span> */}
                  <span className="mt-1 ml-5 w-2 h-2 bg-secondary rounded-full flex-shrink-0"></span>
                  <span className="font-semibold">{item}</span>
                </li>
              ))}
            </div>
          </div>
          <div className="flex flex-col sm:flex-row gap-3 bg-[#E6F0FA] p-5 border border-gray-100 shadow-md rounded-2xl overflow-hidden hover:scale-[1.02] hover:shadow-md transition-all duration-300 border-l-4 border-l-[#0274D9]">
            <div className="w-full md:w-[30%]">
              <Image
                width={0}
                height={0}
                className=" w-full h-auto object-cover object-center transition duration-50"
                loading="lazy"
                src="/images/effortlessMgt.png"
                alt={""}
              />
            </div>

            <div className="w-full md:w-[70%] pl-4 text-black">
              <p className="text-lg md:text-xl font-medium">
                Block Scheduling & Telemedicine Support
              </p>

              <p className="pb-3 pt-4">
                Simplify complex days with block scheduling:
              </p>
              {[
                "Reserve chunks of time for surgeries, drop-offs, or wellness exams",
                "Seamlessly integrate telemedicine appointments alongside in-person visits",
              ].map((item) => (
                <li
                  key={item}
                  className="flex items-center gap-x-2 text-sm pb-1"
                >
                  {/* <span className="mt-1 size-5 flex justify-center items-center rounded-full bg-blue-50 text-blue-600">
                    <svg
                      className="shrink-0  p-1"
                      xmlns="http://www.w3.org/2000/svg"
                      width="24"
                      height="24"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    >
                      <polyline points="20 6 9 17 4 12" />
                    </svg>
                  </span> */}
                  <span className="mt-1 ml-5 w-2 h-2 bg-secondary rounded-full flex-shrink-0"></span>
                  <span className="font-semibold">{item}</span>
                </li>
              ))}
            </div>
          </div>
          <div className="flex flex-col sm:flex-row gap-3 bg-[#E6F0FA] p-5 border border-gray-100 shadow-md rounded-2xl overflow-hidden hover:scale-[1.02] hover:shadow-md transition-all duration-300 border-l-4 border-l-[#0274D9]">
            <div className="w-full md:w-[30%]">
              <Image
                width={0}
                height={0}
                className=" w-full h-auto object-cover object-center transition duration-50"
                loading="lazy"
                src="/images/smartCommunication.png"
                alt={""}
              />
            </div>
            <div className="w-full md:w-[70%] pl-4 text-black">
              <p className="text-lg md:text-xl font-medium">
                Automated Reminders
              </p>

              <p className="pt-4 pb-3">
                Reduce missed appointments with customizable, automated
                reminders sent via SMS, email, or app notifications
              </p>
              {[
                "Custom cadence: Send reminders 1 week before, 1 day before, 1 hour before — you decide",
                "Confirmation capture: Clients can confirm directly from the message with one click",
                "Automatic calendar updates for confirmed or canceled appointments",
              ].map((item) => (
                <li
                  key={item}
                  className="flex items-center gap-x-2 text-sm pb-1"
                >
                  {/* <span className="mt-1 size-5 flex justify-center items-center rounded-full bg-blue-50 text-blue-600">
                    <svg
                      className="shrink-0  p-1"
                      xmlns="http://www.w3.org/2000/svg"
                      width="24"
                      height="24"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    >
                      <polyline points="20 6 9 17 4 12" />
                    </svg>
                  </span> */}
                  <span className="mt-1 ml-5 w-2 h-2 bg-secondary rounded-full flex-shrink-0"></span>
                  <span className="font-semibold">{item}</span>
                </li>
              ))}
            </div>
          </div>
          {/* <div className="flex flex-col sm:flex-row gap-3 bg-[#E6F0FA] p-5 border border-gray-100 shadow-md rounded-2xl overflow-hidden hover:scale-[1.02] hover:shadow-md transition-all duration-300 border-l-4 border-l-[#0274D9]">
            <div className="w-full md:w-[30%]">
              <Image
                width={0}
                height={0}
                className=" w-full h-auto object-cover object-center transition duration-50"
                loading="lazy"
                src="/images/Push-notifications.png"
                alt={""}
              />
            </div>

            <div className="w-full md:w-[70%] pl-4 text-black">
              <p className="text-lg md:text-xl font-medium">
                No-Show Alerts for Staff
              </p>

              <p className=" pt-4">
                If a client misses their appointment, your team is notified
                instantly. Easily follow up, rebook, and keep your schedule full
                without scrambling.
              </p>
            </div>
          </div> */}
        </div>
        <div className="container mx-auto px-4 flex justify-center">
          <div className="w-full lg:max-w-[700px] flex flex-col sm:flex-row gap-3 bg-[#E6F0FA] p-5 border border-gray-100 shadow-md rounded-2xl overflow-hidden hover:scale-[1.02] hover:shadow-md transition-all duration-300 border-l-4 border-l-[#0274D9]">
            <div className="w-full md:w-[30%]">
              <Image
                width={0}
                height={0}
                className=" w-full h-auto object-cover object-center transition duration-50"
                loading="lazy"
                src="/images/Push-notifications.png"
                alt={""}
              />
            </div>

            <div className="w-full md:w-[70%] pl-4 text-black">
              <p className="text-lg md:text-xl font-medium">
                No-Show Alerts for Staff
              </p>

              <p className=" pt-4">
                If a client misses their appointment, your team is notified
                instantly. Easily follow up, rebook, and keep your schedule full
                without scrambling.
              </p>
            </div>
          </div>
        </div>
      </section>

      <section id="cta" className="pb-20">
        <div className="container mx-auto">
          <div className="bg-gradient-to-l from-[#e4f2fe] to-[#fff3fe] text-black md:rounded-[20px] p-4 md:p-0 lg:px-10 xl:px-16">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 items-center">
              {" "}
              {/* Added items-center */}
              <div className="w-full px-4 sm:px-8 flex md:justify-center">
                <div className="py-8 sm:py-12">
                  <h2 className="text-xl sm:text-2xl md:text-3xl mb-7 ">
                    Why Choose Our{"  "}
                    <span className="font-medium italic">
                      Smart Scheduling Tools?
                    </span>
                  </h2>

                  <ul className="space-y-2 sm:space-y-4">
                    {[
                      "Save Administrative Time: Less time on the phone, more time with patients",
                      "Increase Client Satisfaction: Make it easier for pet owners to stay organized",
                      "Maximize Practice Revenue: Keep your schedule full and running on time",
                    ].map((text, idx) => {
                      const [title, description] = text.split(/:(.+)/); // splits at first colon only
                      return (
                        <li key={idx} className="flex gap-x-3 items-start">
                          {/* <span className="mt-1 size-5 flex justify-center items-center rounded-full bg-blue-50 text-blue-600">
                            <svg
                              className="shrink-0  p-1"
                              xmlns="http://www.w3.org/2000/svg"
                              width="24"
                              height="24"
                              viewBox="0 0 24 24"
                              fill="none"
                              stroke="currentColor"
                              strokeWidth="2"
                              strokeLinecap="round"
                              strokeLinejoin="round"
                            >
                              <polyline points="20 6 9 17 4 12" />
                            </svg>
                          </span> */}
                          <span className="mt-2 w-2 h-2 bg-secondary rounded-full flex-shrink-0"></span>
                          <div className="grow">
                            <strong className="font-semibold">{title}</strong>
                            {description && <span>: {description.trim()}</span>}
                          </div>
                        </li>
                      );
                    })}
                  </ul>
                </div>
              </div>
              <div className="w-full px-4 flex justify-center">
                <Image
                  src="/images/Customer-relationship.png"
                  alt="image"
                  className="max-w-full h-auto p-3 sm:px-16 sm:w-2/3"
                  style={{ width: "auto", height: "auto" }}
                  width={0}
                  height={0}
                />
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
};

export default AppointmentManager;
