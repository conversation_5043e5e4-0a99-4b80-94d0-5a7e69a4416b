import Image from "next/image";

const Communication = () => {
  return (
    <div>
      <div className="header">
        <div className="relative isolate py-24 sm:py-42 overflow-hidden">
          <div className="container mx-auto px-4 sm:px-8">
            <div className="absolute inset-0 -z-20 flex justify-end">
              <div className="relative w-1/2 h-full">
                <Image
                  src="/images/communication.avif"
                  alt=""
                  className="absolute inset-0 h-full w-full object-cover object-center"
                  width={0}
                  height={0}
                  priority
                />
              </div>
            </div>

            <div className="absolute inset-0 -z-10 bg-gradient-to-r from-white via-violet-50/100 to-transparent"></div>

            <div className="">
              <div className="mx-auto lg:max-w-3xl lg:mx-0">
                <h2 className="text-3xl md:text-4xl font-medium">
                  Centralized Communication Made Easy with PetVetHub
                </h2>
                <p className="mt-6 text-lg leading-8 text-gray-700">
                  Managing client communication shouldn&apos;t be complicated.
                  With <span className="font-medium">PetVetHub</span>, our
                  all-in-one mobile app and communication platform, you can keep
                  pet owners connected, informed, and engaged — all from a
                  single, easy-to-use system.
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="py-10 md:py-20 container mx-auto px-4 sm:px-8">
        <section className="text-black">
          <div className="container  mx-auto">
            {/* <div className="">
              <h1 className="text-xl sm:text-2xl md:text-3xl">
                PetVetHub Mobile App: <br />{" "}
                <span className="font-medium italic">
                  Your Clinic in Every Pocket
                </span>{" "}
              </h1>

              <p className="mt-4 md:max-w-3xl ">
                The PetVetHub app empowers pet owners to stay in touch with your
                hospital wherever they are. From appointment bookings to
                vaccination updates, everything is just a tap away. <br /> With
                PetVetHub, you can manage communication{" "}
                <span className="font-medium">
                  faster, smarter, and more personally
                </span>
              </p>
            </div> */}

            <div className="w-full px-4 mx-auto text-center">
              <h2 className="font-medium mb-3 text-xl sm:text-2xl md:text-3xl text-black">
                PetVetHub Mobile App: <br />
                <span className="font-medium text-primary">
                  Your Clinic in Every Pocket
                </span>
              </h2>

              <p className="mt-5 md:max-w-4xl mx-auto text-lg leading-8 text-gray-700">
                The PetVetHub app empowers pet owners to stay in touch with your
                hospital wherever they are. From appointment bookings to
                vaccination updates, everything is just a tap away.
                <br className="hidden sm:block" />
                With <span className="font-semibold">PetVetHub</span>, you can manage
                communication <span className="font-medium text-primary">faster, smarter, and more personally</span>.
              </p>
            </div>

            <div className="grid grid-cols-1 gap-8 mt-8 xl:mt-12 md:grid-cols-2 xl:grid-cols-3 text-black">
              <div className="bg-[#E6F0FA] space-y-3 shadow-md hover:scale-[1.02] hover:shadow-md border border-gray-100 rounded-2xl transition-all duration-300 px-4 py-6">
                <span className="inline-block p-4 text-white bg-secondary rounded-2xl">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="w-6d h-6"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  >
                    <rect x="3" y="4" width="18" height="18" rx="2" ry="2" />
                    <line x1="16" y1="2" x2="16" y2="6" />
                    <line x1="8" y1="2" x2="8" y2="6" />
                    <line x1="3" y1="10" x2="21" y2="10" />
                  </svg>
                </span>

                <h1 className="text-xl font-medium">Appointment Bookings</h1>

                <p className="">
                  Clients can request, book, or reschedule appointments anytime
                </p>

                {/* <a
                  href="#"
                  className="inline-flex items-center -mx-1 text-sm text-blue-500 capitalize transition-colors duration-300 transform dark:text-blue-400 hover:underline hover:text-blue-600 dark:hover:text-blue-500"
                >
                  <span className="mx-1">read more</span>
                  <svg
                    className="w-4 h-4 mx-1 rtl:-scale-x-100"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      fill-rule="evenodd"
                      d="M12.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-2.293-2.293a1 1 0 010-1.414z"
                      clip-rule="evenodd"
                    ></path>
                  </svg>
                </a> */}
              </div>

              <div className="bg-[#E6F0FA] space-y-3 shadow-md hover:scale-[1.02] hover:shadow-md border border-gray-100 rounded-2xl transition-all duration-300 px-4 py-6 ">
                <span className="inline-block p-4 text-white bg-secondary rounded-2xl">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="w-6 h-6"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  >
                    <path d="M22 2l-5.586 5.586M14 10l-1 1m7-7l1 1-9 9a1 1 0 01-.293.207l-4 2a1 1 0 01-1.316-1.316l2-4a1 1 0 01.207-.293l9-9z" />
                    <path d="M16 16v6" />
                    <path d="M8 16H2" />
                  </svg>
                </span>

                <h1 className="text-xl font-medium">Vaccination Records</h1>

                <p className="">
                  Give pet owners instant access to up-to-date vaccination
                  history
                </p>
              </div>

              <div className="bg-[#E6F0FA] space-y-3 shadow-md hover:scale-[1.02] hover:shadow-md border border-gray-100 rounded-2xl transition-all duration-300 px-4 py-6 ">
                <span className="inline-block p-4 text-white bg-secondary rounded-2xl">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="w-6 h-6"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  >
                    <path d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V4a2 2 0 10-4 0v1.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 01-6 0v-1m6 0H9" />
                  </svg>
                </span>

                <h1 className="text-xl font-medium">Push Notifications</h1>

                <p className="">
                  Keep clients in the loop with real-time updates and alerts
                </p>
              </div>

              <div className="bg-[#E6F0FA] space-y-3 shadow-md hover:scale-[1.02] hover:shadow-md border border-gray-100 rounded-2xl transition-all duration-300 px-4 py-6 ">
                <span className="inline-block p-4 text-white bg-secondary rounded-2xl">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="w-6 h-6"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  >
                    <path d="M21 15a2 2 0 01-2 2H7l-4 4V5a2 2 0 012-2h14a2 2 0 012 2z" />
                  </svg>
                </span>

                <h1 className="text-xl font-medium">
                  SMS, Email, & App Notifications
                </h1>

                <p className="">
                  Choose the best way to reach every client, automatically
                </p>
              </div>

              <div className="bg-[#E6F0FA] space-y-3 shadow-md hover:scale-[1.02] hover:shadow-md border border-gray-100 rounded-2xl transition-all duration-300 px-4 py-6 ">
                <span className="inline-block p-4 text-white bg-secondary rounded-2xl">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="w-6 h-6"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  >
                    <rect x="3" y="4" width="18" height="18" rx="2" ry="2" />
                    <line x1="16" y1="2" x2="16" y2="6" />
                    <line x1="8" y1="2" x2="8" y2="6" />
                    <line x1="3" y1="10" x2="21" y2="10" />
                    <polyline points="9 16 11.5 18.5 15 15" />
                  </svg>
                </span>

                <h1 className="text-xl font-medium">
                  Reminders & Confirmations
                </h1>

                <p className="">
                  Reduce no-shows with automated reminders and easy
                  confirmations
                </p>
              </div>

              <div className="bg-[#E6F0FA] space-y-3 shadow-md hover:scale-[1.02] hover:shadow-md border border-gray-100 rounded-2xl transition-all duration-300 px-4 py-6 ">
                <span className="inline-block p-4 text-white bg-secondary rounded-2xl">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="w-6 h-6"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  >
                    <path d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2" />
                    <path d="M9 3h6v4H9V3z" />
                    <path d="M9 14l1.5 1.5L14 12" />
                  </svg>
                </span>

                <h1 className="text-xl font-medium">Pre-Visit Instructions</h1>

                <p className="">
                  Share important info before appointments — like fasting,
                  paperwork, or arrival times
                </p>
              </div>

              {/* <div className="bg-[#E6F0FA] space-y-3 shadow-md hover:scale-[1.02] hover:shadow-md border border-gray-100 rounded-2xl transition-all duration-300 px-4 py-6 ">
                <span className="inline-block p-4 text-white bg-secondary rounded-2xl">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="w-6 h-6"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  >
                    <rect x="3" y="4" width="18" height="18" rx="2" ry="2" />
                    <line x1="16" y1="2" x2="16" y2="6" />
                    <line x1="8" y1="2" x2="8" y2="6" />
                    <line x1="3" y1="10" x2="21" y2="10" />
                    <line x1="12" y1="14" x2="12" y2="17" />
                    <line x1="12" y1="20" x2="12" y2="20" />
                  </svg>
                </span>

                <h1 className="text-xl font-medium">
                  Missed Appointment Follow-Ups
                </h1>

                <p className="">
                  If a client misses a visit, PetVetHub triggers a polite
                  follow-up to rebook
                </p>
              </div> */}
            </div>

            <div className="grid grid-cols-1 gap-8 md:mt-8 xl:mt-8 md:grid-cols-2 xl:grid-cols-3 text-black">
              <div></div>
              <div className="bg-[#E6F0FA] space-y-3 shadow-md hover:scale-[1.02] hover:shadow-md border border-gray-100 rounded-2xl transition-all duration-300 px-4 py-6 ">
                <span className="inline-block p-4 text-white bg-secondary rounded-2xl">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="w-6 h-6"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  >
                    <rect x="3" y="4" width="18" height="18" rx="2" ry="2" />
                    <line x1="16" y1="2" x2="16" y2="6" />
                    <line x1="8" y1="2" x2="8" y2="6" />
                    <line x1="3" y1="10" x2="21" y2="10" />
                    <line x1="12" y1="14" x2="12" y2="17" />
                    <line x1="12" y1="20" x2="12" y2="20" />
                  </svg>
                </span>

                <h1 className="text-xl font-medium">
                  Missed Appointment Follow-Ups
                </h1>

                <p className="">
                  If a client misses a visit, PetVetHub triggers a polite
                  follow-up to rebook
                </p>
              </div>
              <div></div>
            </div>

          </div>
        </section>
      </div>

      <section id="cta" className="pb-20">
        <div className="container mx-auto">
          <div className="bg-gradient-to-l from-[#e4f2fe] to-[#fff3fe] text-black md:rounded-[20px] p-4 md:p-0 lg:px-10 xl:px-16">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 items-center">
              {" "}
              {/* Added items-center */}
              <div className="w-full px-4 sm:px-8 flex md:justify-center">
                <div className="py-8 sm:py-12">
                  <h2 className="text-xl sm:text-2xl md:text-3xl mb-7 ">
                    Why PetVetHub?
                  </h2>

                  <ul className="space-y-2 sm:space-y-4">
                    {[
                      "Save Staff Time: Automate routine communication tasks.",
                      "Boost Appointment Rates: Fewer missed visits, more satisfied clients.",
                      "Enhance Client Experience: Make it easy for pet owners to connect and stay loyal.",
                      "Centralize Everything: One platform — all communication — zero confusion.",
                    ].map((text, idx) => {
                      const [title, description] = text.split(/:(.+)/); // splits at first colon only
                      return (
                        <li key={idx} className="flex gap-x-3 items-start">
                          {/* <span className="mt-1 size-5 flex justify-center items-center rounded-full bg-blue-50 text-blue-600">
                            <svg
                              className="shrink-0  p-1"
                              xmlns="http://www.w3.org/2000/svg"
                              width="24"
                              height="24"
                              viewBox="0 0 24 24"
                              fill="none"
                              stroke="currentColor"
                              strokeWidth="2"
                              strokeLinecap="round"
                              strokeLinejoin="round"
                            >
                              <polyline points="20 6 9 17 4 12" />
                            </svg>
                          </span> */}
                          <span className="mt-2 w-2 h-2 bg-secondary rounded-full flex-shrink-0"></span>
                          {/* <span className="mt-2 w-2 h-2 bg-secondary rounded-full flex-shrink-0"></span> */}
                          <div className="grow">
                            <strong className="font-semibold">{title}</strong>
                            {description && <span>: {description.trim()}</span>}
                          </div>
                        </li>
                      );
                    })}
                  </ul>
                </div>
              </div>
              <div className="w-full px-4 flex justify-center">
                <Image
                  src="/images/hand_drawn.png"
                  alt="image"
                  className="max-w-full h-auto p-3 sm:px-16 sm:w-2/3"
                  style={{ width: "auto", height: "auto" }}
                  width={0}
                  height={0}
                />
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
};

export default Communication;
