'use client'
import HoverButton from "@/components/common/hoverButton";
import { <PERSON>, Gem, MousePointerClick, Shield<PERSON>heck, Steth<PERSON>cope, Syringe } from "lucide-react";
import Image from "next/image";

const features = [
    {
        text: "Veterinary-Specific Design",
        icon: Stethoscope,
    },
    {
        text: "Cloud-Based Access from Any Device",
        icon: Cloud,
    },
    {
        text: "HIPAA & DEA Compliance Built-In",
        icon: ShieldCheck,
    },
    {
        text: "Easy to use system",
        icon: MousePointerClick,
    },
];

const Inventory = () => {
    return (
      <div>
        <div className="header">
          <div className="relative isolate py-24 sm:py-42 overflow-hidden">
            <div className="container mx-auto px-4 sm:px-8">
              <div className="absolute inset-0 -z-20 flex justify-end">
                <div className="relative w-1/2 h-full">
                  <Image
                    // src="/images/telemedicine_banner.jpg"
                    src="/images/inventory3.avif"
                    alt=""
                    className="absolute inset-0 h-full w-full object-cover object-center"
                    width={0}
                    height={0}
                    priority
                  />
                </div>
              </div>
              <div className="absolute inset-0 -z-10 bg-gradient-to-r from-white via-[#f7fdff] to-transparent"></div>
              <div className="">
                <div className="mx-auto lg:max-w-3xl lg:mx-0">
                  <h2 className="text-3xl md:text-4xl font-medium">
                    Smart Inventory System for Veterinary Hospitals
                  </h2>
                  <p className="mt-6 text-lg leading-8 text-gray-700">
                    <span className="font-medium">
                      Smarter Control. Safer Practice. Better Profits.{" "}
                    </span>
                    <br />
                    Veterinary hospitals need more than basic inventory
                    tracking—they need smart, secure, and streamlined systems
                    that can handle the complexities of both regulated and
                    high-value items. MyVetHub’s{" "}
                    <span className="font-medium">
                      Smart Inventory System
                    </span>{" "}
                    is purpose-built to meet the unique demands of veterinary
                    practices, giving you real-time control and peace of mind.{" "}
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div className="py-10 container mx-auto">
          <div className="container mx-auto px-4 sm:px-8 lg:grid lg:grid-cols-12 lg:gap-x-12">
            <div className="lg:col-span-5">
              <div className="mb-16 lg:mb-0 xl:mt-16 text-black">
                <h2 className="mb-6 text-xl sm:text-2xl md:text-3xl">
                  <span
                    className={`inline-flex items-center justify-center w-10 h-10 rounded-full shadow-md bg-blue-100 text-blue-700`}
                  >
                    <Syringe className="w-5 h-5" />
                  </span>{" "}
                  Scheduled Drug Inventory Management
                </h2>
                <p className="mb-5">
                  Ensure regulatory compliance and protect your license with
                  robust tracking of all controlled substances.
                </p>
                <p className="sm:text-base font-semibold pb-1">
                  Key Features:{" "}
                </p>
                <ul className="space-y-1 pl-6">
                  {[
                    {
                      label: "DEA-Compliant Logs:",
                      value:
                        "Automatically maintain detailed digital logs in line with DEA, California DOJ and other regulatory requirements.",
                    },
                    {
                      label: "Real-Time Monitoring:",
                      value:
                        "Track dispensation, usage, and inventory levels in real-time across all scheduled drug categories.",
                    },
                    {
                      label: "Role-Based Access:",
                      value:
                        " Restrict access to authorized staff and track every action with secure audit trails.",
                    },
                    {
                      label: "Low Stock Alerts & Reorder Reminders:",
                      value:
                        "Get alerts before you run out—avoid both overstocking and dangerous shortages.",
                    },
                  ].map((item, idx) => (
                    <li key={idx} className="flex gap-x-3">
                      {/* <span className="mt-1 size-5 flex justify-center items-center rounded-full bg-blue-50 text-blue-600">
                                            <svg
                                                className="shrink-0  p-1"
                                                xmlns="http://www.w3.org/2000/svg"
                                                width="24"
                                                height="24"
                                                viewBox="0 0 24 24"
                                                fill="none"
                                                stroke="currentColor"
                                                strokeWidth="2"
                                                strokeLinecap="round"
                                                strokeLinejoin="round"
                                            >
                                                <polyline points="20 6 9 17 4 12" />
                                            </svg>
                                        </span> */}
                      <span className="mt-2 w-2 h-2 bg-secondary rounded-full flex-shrink-0"></span>
                      <div className="grow">
                        {/* text-sm sm:text-base font-semibold */}
                        <span className="font-medium">{item?.label}</span>{" "}
                        {item?.value}
                      </div>
                    </li>
                  ))}
                </ul>

                <p className="sm:text-base font-semibold pt-3 pb-1">
                  Benefits:
                </p>
                <ul className="space-y-1 pl-6">
                  {[
                    "Meet legal and regulatory obligations without manual paperwork.",
                    "Prevent drug diversion and misuse.",
                    "Save hours of admin time and reduce human error.",
                    "Be audit-ready at all times.",
                  ].map((text, idx) => (
                    <li key={idx} className="flex gap-x-3">
                      {/* <span className="mt-1 size-5 flex justify-center items-center rounded-full bg-blue-50 text-blue-600">
                                            <svg
                                                className="shrink-0  p-1"
                                                xmlns="http://www.w3.org/2000/svg"
                                                width="24"
                                                height="24"
                                                viewBox="0 0 24 24"
                                                fill="none"
                                                stroke="currentColor"
                                                strokeWidth="2"
                                                strokeLinecap="round"
                                                strokeLinejoin="round"
                                            >
                                                <polyline points="20 6 9 17 4 12" />
                                            </svg>
                                        </span> */}
                      <span className="mt-2 w-2 h-2 bg-secondary rounded-full flex-shrink-0"></span>
                      <div className="grow">{text}</div>
                    </li>
                  ))}
                </ul>
                {/* <p className="pt-10">
                                Master the digital space with our modern tools.
                            </p> */}
              </div>
            </div>
            <div className="lg:col-span-7">
              <div className="xl:ml-14">
                <Image
                  className="inline sm:p-5"
                  src="/images/inventory-management.jpg"
                  alt="alternative"
                  style={{ width: "auto", height: "auto" }}
                  width={0}
                  height={0}
                />
              </div>
            </div>
          </div>

          <div className="container mx-auto  px-4 sm:px-8 py-10  lg:grid lg:grid-cols-12 lg:gap-x-12 bg-gradient-to-l from-[#e4f2fe] to-[#fff3fe] rounded-[20px]">
            <div className="order-1 lg:order-2 lg:col-span-5">
              <div className="xl:mt-12 text-black">
                <h2 className="mb-6 text-xl sm:text-2xl md:text-3xl">
                  <span
                    className={`inline-flex items-center justify-center w-10 h-10 rounded-full shadow-md bg-white text-blue-700`}
                  >
                    <Gem className="w-5 h-5" />
                  </span>{" "}
                  High-Value Item Inventory
                </h2>

                <div className="mt-5 lg:col-span-5">
                  <div className="space-y-3">
                    <div className="">
                      <p>
                        Protect your profits and stay on top of non-controlled,
                        high-cost inventory—from specialty medications to OTC
                        products.
                      </p>
                    </div>
                    <p className="sm:text-base font-semibold pb-1">
                      Key Features:{" "}
                    </p>
                    <ul className="space-y-1 pl-6">
                      {[
                        {
                          label: "Usage Analytics:",
                          value:
                            "See what’s moving, what’s not, and what’s at risk of expiration.",
                        },
                        {
                          label: "Multi-Location Support:",
                          value:
                            "Track inventory across multiple hospital departments or satellite locations.",
                        },
                        {
                          label: "Loss Prevention Tools:",
                          value:
                            "Identify discrepancies and set alerts for unusual usage or shrinkage. ",
                        },
                      ].map((item, idx) => (
                        <li key={idx} className="flex gap-x-3">
                          {/* <span className="mt-1 size-5 flex justify-center items-center rounded-full bg-blue-50 text-blue-600">
                                                    <svg
                                                        className="shrink-0  p-1"
                                                        xmlns="http://www.w3.org/2000/svg"
                                                        width="24"
                                                        height="24"
                                                        viewBox="0 0 24 24"
                                                        fill="none"
                                                        stroke="currentColor"
                                                        strokeWidth="2"
                                                        strokeLinecap="round"
                                                        strokeLinejoin="round"
                                                    >
                                                        <polyline points="20 6 9 17 4 12" />
                                                    </svg>
                                                </span> */}
                          <span className="mt-2 w-2 h-2 bg-secondary rounded-full flex-shrink-0"></span>
                          <div className="grow">
                            <span className="font-medium">{item?.label}</span>{" "}
                            {item?.value}
                          </div>
                        </li>
                      ))}
                    </ul>

                    <p className="sm:text-base font-semibold pt-3 pb-1">
                      Benefits:
                    </p>
                    <ul className="space-y-1 pl-6">
                      {[
                        "Maximize revenue by reducing inventory waste and loss.",
                        "Improve forecasting and purchasing accuracy",
                        "Increase staff accountability.",
                        "Keep shelves stocked with what your team and clients need most.",
                      ].map((text, idx) => (
                        <li key={idx} className="flex gap-x-3">
                          {/* <span className="mt-1 size-5 flex justify-center items-center rounded-full bg-blue-50 text-blue-600">
                                                    <svg
                                                        className="shrink-0  p-1"
                                                        xmlns="http://www.w3.org/2000/svg"
                                                        width="24"
                                                        height="24"
                                                        viewBox="0 0 24 24"
                                                        fill="none"
                                                        stroke="currentColor"
                                                        strokeWidth="2"
                                                        strokeLinecap="round"
                                                        strokeLinejoin="round"
                                                    >
                                                        <polyline points="20 6 9 17 4 12" />
                                                    </svg>
                                                </span> */}
                          <span className="mt-2 w-2 h-2 bg-secondary rounded-full flex-shrink-0"></span>
                          <div className="grow">{text}</div>
                        </li>
                      ))}
                    </ul>
                  </div>
                </div>
              </div>
            </div>
            <div className="order-2 lg:order-1 lg:col-span-7">
              <div className="mb-12 lg:mb-0 xl:mr-14">
                <Image
                  className="inline p-5 sm:px-14"
                  // src="/images/online_calendar.png"
                  src="/images/highvloume.jpg"
                  alt="alternative"
                  style={{ width: "auto", height: "auto" }}
                  width={0}
                  height={0}
                />
              </div>
            </div>
          </div>
        </div>

        {/*start Why Choose */}
        <section className="py-10 md:py-16 px-4 sm:px-8 lg:px-20">
          <div className="max-w-7xl mx-auto">
            <div className="text-center mb-5 md:mb-10">
              <h2 className="text-2xl md:text-4xl">
                Why Choose Our Smart Inventory System?
              </h2>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-5 md:gap-8">
              {features.map(({ text, icon: Icon }, idx) => (
                <div
                  key={idx}
                  className="bg-[#E6F0FA] p-6 rounded-2xl shadow hover:shadow-md transition duration-300"
                >
                  <div className="flex items-start gap-3">
                    <Icon className="w-9 h-9 text-[#0274D9] mt-1" />
                    <p className="text-gray-800">{text}</p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </section>
        {/*end Why Choose */}

        <section className="bg-gradient-to-l from-[#e4f2fe] to-[#fff3fe] py-16">
          <div className="text-center px-4 max-w-4xl mx-auto">
            <p className="text-2xl md:text-4xl">
              Ready to Simplify Your Inventory Management?
            </p>
            <p className="text-lg md:text-xl pt-5">
              Contact us today for a free demo and see how smart your practice
              can be.{" "}
            </p>
            <div className="mt-6 flex justify-center">
              <HoverButton text="Contact us" link="/contact" className="" />
            </div>
          </div>
        </section>
      </div>
    );
};

export default Inventory;
