import { Activity, Calendar<PERSON><PERSON><PERSON>, <PERSON>, PawP<PERSON>t, ShieldCheck, Smartphone, Smile, Wallet } from "lucide-react";
import Image from "next/image";

const reasons = [
    {
        icon: <Activity className="w-5 h-5" />,
        title: "DVM-Centric Design",
        desc: "Every feature is built to enhance the veterinarian's workflow.",
    },
    {
        icon: <Clock className="w-5 h-5" />,
        title: "Increased Efficiency",
        desc: "Spend more time on care, less on admin.",
    },
    {
        icon: <Smile className="w-5 h-5" />,
        title: "Improved Client Experience",
        desc: "Convenience and transparency lead to happier pet parents.",
    },
    {
        icon: <ShieldCheck className="w-5 h-5" />,
        title: "Revenue Protection",
        desc: "Upfront payments reduce financial risk and no-shows.",
    },
];

const Telemedicine = () => {
    return (
        <div>
            <div className="header">
                <div className="relative isolate py-24 sm:py-42 overflow-hidden">
                    <div className="container mx-auto px-4 sm:px-8">
                        <div className="absolute inset-0 -z-20 flex justify-end">
                            <div className="relative w-1/2 h-full">
                                <Image
                                    src="/images/telemedicine.avif"
                                    // src="/images/telemedicine-banner.jpg"
                                    // src={'/images/telemedicine_appointment_booking.jpg'}
                                    alt=""
                                    className="absolute inset-0 h-full w-full object-cover object-center"
                                    width={0}
                                    height={0}
                                    priority
                                />
                            </div>
                        </div>
                        <div className="absolute inset-0 -z-10 bg-gradient-to-r from-white via-sky-100 to-transparent"></div>
                        <div className="">
                            <div className="mx-auto lg:max-w-3xl lg:mx-0">
                                <h2 className="text-3xl md:text-4xl font-medium">
                                    <span className={`inline-flex items-center justify-center w-10 h-10 rounded-full shadow-md bg-blue-100 text-blue-700`}>
                                        <Smartphone className="w-5 h-5" />
                                    </span>
                                    {" "}
                                    Telemedicine & Appointment Booking for Veterinary Hospitals  </h2>
                                <p className="mt-6 text-lg leading-8 text-gray-700">
                                    <span className="font-medium">Designed for DVMs. Built for Efficiency. Loved by Clients. </span><br />
                                    Streamline your hospital operations and enhance client satisfaction with our purpose-built <span className="font-medium">Telemedicine</span> and <span className="font-medium">Online Appointment Booking</span> platform—crafted specifically for veterinary practices. Keep your focus on delivering excellent patient care while we take care of the scheduling and virtual visit logistics.
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div className="py-10 container mx-auto">
                <div className="container mx-auto px-4 sm:px-8 lg:grid lg:grid-cols-12 lg:gap-x-12">
                    <div className="lg:col-span-5">
                        <div className="mb-16 lg:mb-0 xl:mt-16 text-black">
                            <h2 className="mb-6 text-xl sm:text-2xl md:text-3xl">
                                <span className={`inline-flex items-center justify-center w-10 h-10 rounded-full shadow-md bg-blue-100 text-blue-700`}>
                                    <Wallet className="w-5 h-5" />
                                </span>
                                {" "}Telemedicine with Upfront Payment
                            </h2>
                            <p className="mb-5">
                                <span className="font-medium"> Convenient Virtual Visits with Guaranteed Revenue </span>
                                Provide high-quality remote consultations without the administrative hassle. Our integrated telemedicine feature allows pet parents to connect with your team through secure video appointments—all while requiring <span className="font-medium">upfront payment</span> to reduce no-shows and ensure commitment.
                            </p>
                            <p className="sm:text-base font-semibold pb-1">Key Features: </p>
                            <ul className="space-y-1 pl-6">
                                {[
                                    "Secure, HIPAA-compliant video platform ",
                                    "Customizable visit types (triage, follow-up, rechecks, behavior, nutrition, etc.)",
                                    "Integrated online payments collected before visit confirmation ",
                                    "DVM dashboard with notes, files, and client history access",
                                    "Easy link sharing and appointment reminders"
                                ].map((text, idx) => (
                                    <li key={idx} className="flex gap-x-3">
                                        {/* <span className="mt-1 size-5 flex justify-center items-center rounded-full bg-blue-50 text-blue-600">
                                            <svg
                                                className="shrink-0  p-1"
                                                xmlns="http://www.w3.org/2000/svg"
                                                width="24"
                                                height="24"
                                                viewBox="0 0 24 24"
                                                fill="none"
                                                stroke="currentColor"
                                                strokeWidth="2"
                                                strokeLinecap="round"
                                                strokeLinejoin="round"
                                            >
                                                <polyline points="20 6 9 17 4 12" />
                                            </svg>
                                        </span> */}
                                        <span className="mt-2 w-2 h-2 bg-secondary rounded-full flex-shrink-0"></span>
                                        <div className="grow">
                                            {/* text-sm sm:text-base font-semibold */}
                                            {text}
                                        </div>
                                    </li>
                                ))}
                            </ul>

                            <p className="sm:text-base font-semibold pt-3 pb-1">Benefits:</p>
                            <ul className="space-y-1 pl-6">
                                {[
                                    "Reduce missed appointments with pre-paid visits",
                                    "Expand service hours and geographic reach",
                                    "Provide care for non-urgent cases without clinic congestion",
                                    "Save staff time with automated scheduling and payments",
                                ].map((text, idx) => (
                                    <li key={idx} className="flex gap-x-3">
                                        {/* <span className="mt-1 size-5 flex justify-center items-center rounded-full bg-blue-50 text-blue-600">
                                            <svg
                                                className="shrink-0  p-1"
                                                xmlns="http://www.w3.org/2000/svg"
                                                width="24"
                                                height="24"
                                                viewBox="0 0 24 24"
                                                fill="none"
                                                stroke="currentColor"
                                                strokeWidth="2"
                                                strokeLinecap="round"
                                                strokeLinejoin="round"
                                            >
                                                <polyline points="20 6 9 17 4 12" />
                                            </svg>
                                        </span> */}
                                        <span className="mt-2 w-2 h-2 bg-secondary rounded-full flex-shrink-0"></span>
                                        <div className="grow">
                                            {text}
                                        </div>
                                    </li>
                                ))}
                            </ul>
                            {/* <p className="pt-10">
                                Master the digital space with our modern tools.
                            </p> */}
                        </div>
                    </div>
                    <div className="lg:col-span-7 flex items-center">
                        {/* <div className="xl:ml-14">
                            <Image
                                className="inline sm:p-5"
                                src="/images/online-doctor-talking-patient.png"
                                alt="alternative"
                                style={{ width: "auto", height: "auto" }}
                                width={0}
                                height={0}
                            />
                        </div> */}
                        <div className="w-full rounded-lg overflow-hidden p-5 sm:px-14">
                            <Image
                                src="/images/myvethub_tele_02.webp"
                                alt="alternative"
                                width={600}
                                height={400}
                                className="w-full h-auto object-contain rounded-lg"
                            />
                        </div>

                    </div>
                </div>

                <div className="container mx-auto  px-4 sm:px-8 py-10 my-10  lg:grid lg:grid-cols-12 lg:gap-x-12 bg-gradient-to-l from-[#e4f2fe] to-[#fff3fe] rounded-[20px]">
                    <div className="order-1 lg:order-2 lg:col-span-5">
                        <div className="xl:mt-12 text-black">
                            <h2 className="mb-6 text-xl sm:text-2xl md:text-3xl">
                                <span className={`inline-flex items-center justify-center w-10 h-10 rounded-full shadow-md bg-white text-blue-700`}>
                                    <CalendarCheck className="w-5 h-5" />
                                </span>
                                {" "} Online Appointment Booking System
                            </h2>

                            <div className="mt-5 lg:col-span-5">
                                <div className="space-y-3">
                                    <div className="">
                                        <p>
                                            <span className="font-medium">Empower Clients. Free Up Staff Time. </span><br />
                                            Let pet owners book appointments anytime, anywhere, on their schedule. Our intuitive online booking system is synced with your hospital’s availability and preferences, so DVMs remain in control without interruptions to clinic flow.
                                        </p>
                                    </div>
                                    <p className="sm:text-base font-semibold pb-1">Key Features: </p>
                                    <ul className="space-y-1 pl-6">
                                        {[
                                            "Real-time availability synced with your calendar",
                                            "Custom rules for appointment types, durations, and DVM preferences",
                                            "Email/SMS reminders and confirmations",
                                            "Mobile-responsive interface for easy access",
                                            "Seamless integration with your practice management software"
                                        ].map((text, idx) => (
                                            <li key={idx} className="flex gap-x-3">
                                                {/* <span className="mt-1 size-5 flex justify-center items-center rounded-full bg-blue-50 text-blue-600">
                                                    <svg
                                                        className="shrink-0  p-1"
                                                        xmlns="http://www.w3.org/2000/svg"
                                                        width="24"
                                                        height="24"
                                                        viewBox="0 0 24 24"
                                                        fill="none"
                                                        stroke="currentColor"
                                                        strokeWidth="2"
                                                        strokeLinecap="round"
                                                        strokeLinejoin="round"
                                                    >
                                                        <polyline points="20 6 9 17 4 12" />
                                                    </svg>
                                                </span> */}
                                                <span className="mt-2 w-2 h-2 bg-secondary rounded-full flex-shrink-0"></span>
                                                <div className="grow">
                                                    {text}
                                                </div>
                                            </li>
                                        ))}
                                    </ul>

                                    <p className="sm:text-base font-semibold pt-3 pb-1">Benefits:</p>
                                    <ul className="space-y-1 pl-6">
                                        {[
                                            "Eliminate phone tag and reduce front-desk load",
                                            "Increase bookings outside regular hours",
                                            "Improve client satisfaction and retention",
                                            "Maintain control over schedule visibility and booking policies",
                                        ].map((text, idx) => (
                                            <li key={idx} className="flex gap-x-3">
                                                {/* <span className="mt-1 size-5 flex justify-center items-center rounded-full bg-blue-50 text-blue-600">
                                                    <svg
                                                        className="shrink-0  p-1"
                                                        xmlns="http://www.w3.org/2000/svg"
                                                        width="24"
                                                        height="24"
                                                        viewBox="0 0 24 24"
                                                        fill="none"
                                                        stroke="currentColor"
                                                        strokeWidth="2"
                                                        strokeLinecap="round"
                                                        strokeLinejoin="round"
                                                    >
                                                        <polyline points="20 6 9 17 4 12" />
                                                    </svg>
                                                </span> */}
                                                <span className="mt-2 w-2 h-2 bg-secondary rounded-full flex-shrink-0"></span>
                                                <div className="grow">
                                                    {text}
                                                </div>
                                            </li>
                                        ))}
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div className="order-2 lg:order-1 lg:col-span-7 flex items-center">
                        {/* <div className="mb-12 lg:mb-0 xl:mr-14"> */}
                            {/* <Image
                                className="inline p-5 sm:px-14 rounded-lg"
                                // src="/images/online_calendar.png"
                                // src='/images/bookappointment.jpg'
                                src='/images/myvethub_appointment.webp'
                                alt="alternative"
                                style={{ width: "auto", height: "auto" }}
                                width={0}
                                height={0}
                            /> */}
                            <div className="w-full rounded-lg overflow-hidden p-5 sm:px-14">
                                <Image
                                    src="/images/myvethub_appointment.webp"
                                    alt="alternative"
                                    width={600}
                                    height={400}
                                    className="w-full h-auto object-contain rounded-lg"
                                />
                            </div>
                        {/* </div> */}
                    </div>
                </div>

            </div>
            {/* why choose us */}
            <div className="py-10 md:py-16">
                <div className="container mx-auto px-4 sm:px-8 text-center">
                    <h2 className="text-2xl sm:text-3xl md:text-4xl font-medium mb-10">
                        <span className="inline-flex items-center justify-center w-10 h-10 rounded-full bg-blue-100 text-blue-700 shadow-md">
                            <PawPrint className="w-5 h-5" />
                        </span>
                        {" "} Why Veterinary Practices Choose Us
                    </h2>
                    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-8 text-left">
                        {reasons.map((item, idx) => (
                            <div key={idx} className="p-6 bg-[#E6F0FA] rounded-xl shadow-md hover:shadow-lg transition">
                                <div className="mb-4 inline-flex items-center justify-center w-12 h-12 rounded-full bg-blue-100 text-blue-700">
                                    {item.icon}
                                </div>
                                <h3 className="text-lg font-semibold mb-2">{item.title}</h3>
                                <p className="text-gray-600 text-sm">{item.desc}</p>
                            </div>
                        ))}
                    </div>
                </div>
            </div>
        </div>
    );
};

export default Telemedicine;
