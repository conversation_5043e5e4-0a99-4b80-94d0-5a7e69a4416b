import Image from "next/image";

const Marketing = () => {
  return (
    <div>
      <div className="header">
        <div className="relative isolate py-24 sm:py-42 overflow-hidden">
          <div className="container mx-auto px-4 sm:px-8">
            <div className="absolute inset-0 -z-20 flex justify-end">
              <div className="relative w-1/2 h-full">
                <Image
                  // src="/images/marketing_bot.jpeg"
                  src={'/images/marketing.avif'}
                  alt=""
                  className="absolute inset-0 h-full w-full object-cover object-center"
                  width={0}
                  height={0}
                  priority
                />
              </div>
            </div>

            <div className="absolute inset-0 -z-10 bg-gradient-to-r from-white via-sky-50/100 to-transparent"></div>
            {/* bg-gradient-to-r from-[#1a2b3c] via-[#2b3e50] to-transparent */}

            <div className="">
              <div className="mx-auto lg:max-w-3xl lg:mx-0">
                <h2 className="text-3xl md:text-4xl font-medium">Digital Brand & Reputation </h2>
                <p className="mt-6 text-lg leading-8 text-gray-700">
                  At <span className="font-medium">MyVetHub</span>, we specialize in helping veterinary hospitals grow, connect with pet owners, and stand out online. Whether you’re a single-location hospital or a multi-location enterprise, we deliver custom digital solutions designed for your unique needs.
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="py-10 md:py-20 container mx-auto">
        <div className="container mx-auto px-4 sm:px-8 lg:grid lg:grid-cols-12 lg:gap-x-12 mb-5">
          <div className="lg:col-span-5">
            <div className="mb-16 lg:mb-0 xl:mt-16 text-black">
              <h2 className="mb-6 text-xl sm:text-2xl md:text-3xl">
                {/* <span className="font-medium italic"> Custom Websites </span>{" "}
                for Veterinary Clinics */}
                Websites & more for Veterinary Hospitals
              </h2>
              <p className="mb-6">
                Let your digital presence exude trust with compassionate appeal through a customized website with state-of-the-art User Experience, quality SEO, impactful Social Media marketing, multi-channel paid advertising and review management.
              </p>

              <ul className="pl-5 space-y-2 sm:space-y-4">
                {[
                  "Mobile-friendly designs to reach pet owners on the go",
                  "Search engine optimized (SEO) to help you rank higher on Google",
                  "Constantly optimizing SEO for changes to Google site rank algorithm",
                  "Thoughtfully crafted User journeys for profitable results",
                ].map((text, idx) => (
                  <li key={idx} className="flex gap-x-3 items-start">
                    {/* <span className="mt-1 size-5 flex justify-center items-center rounded-full bg-blue-50 text-blue-600">
                      <svg
                        className="shrink-0  p-1"
                        xmlns="http://www.w3.org/2000/svg"
                        width="24"
                        height="24"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                        strokeWidth="2"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                      >
                        <polyline points="20 6 9 17 4 12" />
                      </svg>
                    </span> */}
                    <span className="mt-2 w-2 h-2 bg-secondary rounded-full flex-shrink-0"></span>
                    <div className="grow text-sm sm:text-base font-semibold">
                      {text}
                    </div>
                  </li>
                ))}
              </ul>
              <p className="pt-10">
                Master the digital space with our modern tools.
              </p>
            </div>
          </div>
          <div className="lg:col-span-7">
            <div className="xl:ml-14">
              {/* <Image
                className="inline sm:p-5"
                src="/images/customWebsite.png"
                alt="alternative"
                style={{ width: "auto", height: "auto" }}
                width={0}
                height={0}
              /> */}
              <div className="rounded-lg overflow-hidden w-full max-w-[600px] mx-auto">
                <Image
                  src="/images/myvethub_website.webp"
                  alt="Website Example"
                  width={600}
                  height={400}
                  className="w-full h-auto object-contain rounded-lg"
                />
              </div>
            </div>
          </div>
        </div>

        <div className="container mx-auto bg-gradient-to-l from-[#e4f2fe] to-[#fff3fe] rounded-[20px] px-4 sm:px-8 lg:grid lg:grid-cols-12 lg:gap-x-12">
          <div className="order-1 lg:order-2 lg:col-span-5">
            <div className="xl:mt-12 text-black">
              <h2 className="mb-6 text-xl sm:text-2xl md:text-3xl">
                {/* Pet Owner{" "}
                <span className="font-medium italic"> Engagement Tools</span> */}
                From Engagement to Trust
              </h2>

              <div className="mt-5 mb-10 lg:col-span-5">
                <div className="space-y-6 sm:space-y-8">
                  <div className="mb-6">
                    <p>
                      Make use of our carefully crafted social media campaigns that brings loyalty and cements your practice as a trustworthy enterprise.
                    </p>
                  </div>

                  <ul className="pl-5 space-y-2 sm:space-y-4">
                    {[
                      "We bring in engaging content that is informative & humorous",
                      "Targeted Google, Meta & Instagram Ads to capture local searchers",
                      "Dynamic Social Media Campaigns that engage your community",
                      "We provide Data-driven strategies to maximize your return on investment"
                    ].map((text, idx) => (
                      <li key={idx} className="flex gap-x-3 items-start">
                        {/* <span className="mt-1 size-5 flex justify-center items-center rounded-full bg-blue-50 text-blue-600">
                          <svg
                            className="shrink-0  p-1"
                            xmlns="http://www.w3.org/2000/svg"
                            width="24"
                            height="24"
                            viewBox="0 0 24 24"
                            fill="none"
                            stroke="currentColor"
                            strokeWidth="2"
                            strokeLinecap="round"
                            strokeLinejoin="round"
                          >
                            <polyline points="20 6 9 17 4 12" />
                          </svg>
                        </span> */}
                        <span className="mt-2 w-2 h-2 bg-secondary rounded-full flex-shrink-0"></span>
                        <div className="grow text-sm sm:text-base font-semibold">
                          {text}
                        </div>
                      </li>
                    ))}
                  </ul>

                  <p className="pt-2">
                    Let’s get your services seen by the right people, at the right time
                  </p>
                </div>
              </div>
            </div>
          </div>
          <div className="order-2 lg:order-1 lg:col-span-7">
            <div className="mb-12 lg:mb-0 xl:mr-14">
              <Image
                className="inline p-5 sm:px-14"
                // src="/images/online_calendar.png"
                src='/images/social_Dashboard.png'
                alt="alternative"
                style={{ width: "auto", height: "auto" }}
                width={0}
                height={0}
              />
            </div>
          </div>
        </div>

        {/* <div className="container mx-auto px-4 sm:px-8 lg:grid lg:grid-cols-12 lg:gap-x-12">
          <div className="lg:col-span-5">
            <div className="mb-16 lg:mb-0 xl:mt-16 text-black">
              <h2 className="mb-6 text-xl sm:text-2xl md:text-3xl">
                Boost Your Visibility with{" "}
                <span className="font-medium italic"> Digital Campaigns</span>
              </h2>
              <p className="mb-6">
                Forget traditional “advertisements” — we craft high-performing
                digital campaigns that bring pet owners right to your door:
              </p>

              <ul className="space-y-2 sm:space-y-4">
                {[
                  "Targeted Google Ads to capture local searchers",
                  "Dynamic Social Media Campaigns that engage your community",
                  "Data-driven strategies to maximize your return on investment",
                ].map((text, idx) => (
                  <li key={idx} className="flex gap-x-3 items-start">
                    <span className="mt-1 size-5 flex justify-center items-center rounded-full bg-blue-50 text-blue-600 ">
                      <svg
                        className="shrink-0  p-1"
                        xmlns="http://www.w3.org/2000/svg"
                        width="24"
                        height="24"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                        strokeWidth="2"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                      >
                        <polyline points="20 6 9 17 4 12" />
                      </svg>
                    </span>
                    <div className="grow text-sm sm:text-base font-semibold">
                      {text}
                    </div>
                  </li>
                ))}
              </ul>

              <p className="pt-10">
                Let’s get your services seen by the right people, at the right
                time
              </p>
            </div>
          </div>
          <div className="lg:col-span-7">
            <div className="md:ml-14">
              <Image
                className="inline p-3 sm:px-10"
                src="/images/social_Dashboard.png"
                alt="alternative"
                style={{ width: "auto", height: "auto" }}
                width={0}
                height={0}
              />
            </div>
          </div>
        </div> */}

        {/* A strong local presence section start */}
        <div className="container mx-auto px-4 sm:px-8 lg:grid lg:grid-cols-12 lg:gap-x-12">
          <div className="lg:col-span-5">
            <div className="mb-16 lg:mb-0 xl:mt-16 text-black">
              <h2 className="mb-6 text-xl sm:text-2xl md:text-3xl">
                {/* Boost Your Visibility with{" "}
                <span className="font-medium italic"> Digital Campaigns</span> */}
                A strong local presence is essential for veterinary practices
              </h2>
              <p className="mb-6">
                We manage your internet presence including Google My Business profile, Meta & Instagram Page, Yelp listing and other local directories.
              </p>

              <ul className="pl-5 space-y-2 sm:space-y-4">
                {[
                  "We keep your business info up to date across all these channels.",
                  "We respond to reviews in a timely manner and have a plan to get more 5-star review.",
                  "We improve search visibility",
                  "Gather more 5-star reviews",
                  "Ensure your hospital information is always accurate and up to date"
                ].map((text, idx) => (
                  <li key={idx} className="flex gap-x-3 items-start">
                    {/* <span className="mt-1 size-5 flex justify-center items-center rounded-full bg-blue-50 text-blue-600 ">
                      <svg
                        className="shrink-0  p-1"
                        xmlns="http://www.w3.org/2000/svg"
                        width="24"
                        height="24"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                        strokeWidth="2"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                      >
                        <polyline points="20 6 9 17 4 12" />
                      </svg>
                    </span> */}
                    <span className="mt-2 w-2 h-2 bg-secondary rounded-full flex-shrink-0"></span>
                    <div className="grow text-sm sm:text-base font-semibold">
                      {text}
                    </div>
                  </li>
                ))}
              </ul>

              <p className="pt-10">
                Own your local market — and watch your practice grow
              </p>
            </div>
          </div>
          <div className="lg:col-span-7">
            <div className="md:ml-14">
              <Image
                className="inline p-3 sm:px-10"
                src="/images/SEO_img.png"
                alt="alternative"
                style={{ width: "auto", height: "auto" }}
                width={0}
                height={0}
              />
            </div>
          </div>
        </div>
        {/* A strong local presence section end */}

        {/* <section id="cta" className="pt-20">
          <div className="container mx-auto">
            <div className="bg-gradient-to-l from-[#e4f2fe] to-[#fff3fe] text-black md:rounded-[20px] p-4 md:p-0 lg:px-10 xl:px-16">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 items-center">
                {" "}
                <div className="w-full px-4 sm:px-8 flex md:justify-center">
                  <div className="py-8 sm:py-12">
                    <h2 className="text-xl sm:text-2xl md:text-3xl mb-7 ">
                      A strong local presence is essential for{" "}
                      <span className="font-medium italic">
                        veterinary practices
                      </span>
                    </h2>
                    <p className="mb-2">
                      A strong local presence is essential for veterinary
                      practices
                    </p>
                    <p className="mb-6">
                      We manage your Google My Business profile and other local
                      directories to:
                    </p>
                    <ul className="space-y-2 sm:space-y-4">
                      {[
                        "Improve search visibility",
                        "Gather more 5-star reviews",
                        "Ensure your clinic information is always accurate and up to date",
                      ].map((text, idx) => (
                        <li key={idx} className="flex gap-x-3 items-start">
                          <span className="mt-1 size-5 flex justify-center items-center rounded-full bg-blue-50 text-blue-600">
                            <svg
                              className="shrink-0  p-1"
                              xmlns="http://www.w3.org/2000/svg"
                              width="24"
                              height="24"
                              viewBox="0 0 24 24"
                              fill="none"
                              stroke="currentColor"
                              strokeWidth="2"
                              strokeLinecap="round"
                              strokeLinejoin="round"
                            >
                              <polyline points="20 6 9 17 4 12" />
                            </svg>
                          </span>
                          <div className="grow text-sm sm:text-base font-semibold">
                            {text}
                          </div>
                        </li>
                      ))}
                    </ul>
                    <p className="pt-10">
                      Own your local market — and watch your practice grow
                    </p>
                  </div>
                </div>
                <div className="w-full px-4 flex justify-center">
                  <Image
                    src="/images/SEO_img.png"
                    alt="image"
                    className="max-w-full h-auto p-3 sm:px-16 sm:w-2/3"
                    style={{ width: "auto", height: "auto" }}
                    width={0}
                    height={0}
                  />
                </div>
              </div>
            </div>
          </div>
        </section> */}
      </div>
    </div>
  );
};

export default Marketing;
