
import { CheckCircle } from 'lucide-react';
import React from 'react';

const PaymentSuccess: React.FC = () => {
  return (
    <div className="min-h-screen flex flex-col items-center justify-center text-center px-4">
      <CheckCircle className="text-green-600 w-16 h-16 mb-4" />
      <h1 className="text-2xl md:text-4xl font-bold text-green-700 mb-2">
        Payment Successful
      </h1>
      <p className="text-lg text-gray-700 hidden md:block">
        Thank you! Your payment has been received and your appointment is confirmed.
      </p>
      <p className="text-lg mb-6 text-gray-700 hidden md:block">
        You will receive the booking details on your registered phone number shortly.
      </p>
      <p className="text-md text-gray-700 md:hidden mb-6">
        Your appointment is confirmed. <br />
        Details will be sent to your phone.
      </p>
      {/* <Link
        href="/"
        className="bg-green-600 text-white px-6 py-2 rounded hover:bg-green-700 transition"
      >
        Go to Home
      </Link> */}
        <p className="text-gray-600">You may now close this window.</p>
    </div>
  );
};

export default PaymentSuccess;
