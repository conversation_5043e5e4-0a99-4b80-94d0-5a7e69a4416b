import PageBanner from "@/components/pageBanner";

const DeleteAccount = () => {
  return (<>
    <div className="about-banner mt-10">
      <PageBanner
        title="Delete Account"
        homeText=""
        imageUrl="/images/Lift-&-Shift-min.png"
      />
    </div>
    <div className="container mx-auto px-4 py-10 max-w-4xl">
      <h2 className="text-2xl md:text-3xl font-bold mb-6">Steps to Delete Your Account</h2>
      <ul className="pl-6 list-disc list-outside space-y-2">
        <li>Log in to the app.</li>
        <li>Go to the Profile screen.</li>
        <li>
          In the list of options, tap on <strong>Delete Account</strong>.
        </li>
        <li>You will be prompted to confirm the deletion.</li>
        <li>
          Tap <strong>Confirm</strong> to proceed. Your account will then be deleted.
        </li>
      </ul>

      <h2 className="text-2xl md:text-3xl font-bold mt-10 mb-4">Data Retention Policy</h2>
      <p className="mb-6">After account deletion:</p>
      <ul className="pl-6 list-disc list-outside space-y-2">
        <li>Your data will be retained securely for 30 days.</li>
        <li>
          If you log in again within those 30 days, your data will be restored automatically.
        </li>
        <li>
          If no login attempt is made within 30 days, your data will be permanently deleted and cannot be recovered.
        </li>
      </ul>
    </div>
  </>
  );
};

export default DeleteAccount;
