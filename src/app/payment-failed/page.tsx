
import { XCircle } from 'lucide-react';
import React from 'react';

const PaymentFailed: React.FC = () => {
  return (
    <div className="min-h-screen flex flex-col items-center justify-center text-center px-4">
      <div className='md:w-[50%]'>
        <div className='flex justify-center'>
          <XCircle className="text-red-600 w-16 h-16 mb-4" />
        </div>
        <h1 className="text-2xl md:text-4xl font-bold text-red-700 mb-2">
          Payment Failed
        </h1>
        <p className="text-lg text-gray-700 hidden md:block">
          We were unable to complete your transaction.
        </p>
        <p className="text-lg mb-6 text-gray-700 hidden md:block">
          This may be due to a network issue, incorrect card details, or a payment interruption.
        </p>
        <p className="text-lg font-semibold text-gray-700 hidden md:block">What you can do:</p>
        <ul className='hidden md:block'>
          <li className="text-lg text-gray-700">Try again using the same or a different payment method</li>
          <li className="text-lg mb-6 text-gray-700">Contact <NAME_EMAIL></li>
          {/* at +1(408) 372-5811 */}
        </ul>

        <p className="text-md text-gray-700 md:hidden mb-6">
          We couldn&apos;t process your payment. <br />
          Please try again or use a different method.
        </p>

        {/* <Link
          href="/"
          className="bg-red-600 text-white px-6 py-2 rounded hover:bg-red-700 transition"
        >
          Go to Home
        </Link> */}
        <p className="text-gray-600">You may now close this window.</p>
      </div>
    </div>
  );
};

export default PaymentFailed;
