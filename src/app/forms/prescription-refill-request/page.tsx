"use client";

import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Calendar } from "@/components/ui/calendar";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { countryPhoneCodes } from "@/constants/common-constant";
import { cleanPhoneNumber } from "@/constants/dateTime";
import { format } from "date-fns";
import dayjs from "dayjs";
import timezone from "dayjs/plugin/timezone";
import { CalendarIcon, Clock, Mail, PawPrint, Phone, X } from "lucide-react";
import Image from "next/image";
import { useState } from "react";
dayjs.extend(timezone);

export default function Component() {
  // let CalendarID = "2acb3346-6c60-45ed-8bb0-b9da4f702b7a";
  // let ProviderID = "606b5bbf-abe2-4095-a12b-609b8c0d52e8";

  const [selectedCountry] = useState(countryPhoneCodes[0]);
  const [loading, setLoading] = useState(false);
  const [success, setSuccess] = useState(false);

  const [isDatePickerOpen, setIsDatePickerOpen] = useState(false);

  // const { postData: postContactSendData, isLoading: postContactAPILoading } =
  //     usePostApi("");
  //   const { notify } = useNotificationContext();

  const [formData, setFormData] = useState({
    ownerName: "",
    phone: "",
    petName: "",
    petType: "",
    petGender: "",
    prescriptionName: "",
    lastRefillDate: undefined as Date | undefined,
    veterinarian: "",
    additionalNotes: "",
  });

  const [formErrors, setFormErrors] = useState<{
    ownerName?: string;
    phone?: string;
    petName?: string;
    petType?: string;
    petGender?: string;
    prescriptionName?: string;
  }>({});

  const cleanedPhone = cleanPhoneNumber(formData?.phone);
  const userTimezone = "America/Los_Angeles";

  const handleInputChange = (
    field: string,
    value: string | Date | undefined
  ) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
  };

  const handleLastRefillSelect = (date: Date | undefined) => {
    if (!date) {
      setIsDatePickerOpen(false);
      return;
    }

    const formattedDate = dayjs(date).format("YYYY-MM-DD");
    const currentDateFormatted = formData.lastRefillDate
      ? dayjs(formData.lastRefillDate).format("YYYY-MM-DD")
      : "";

    if (formattedDate === currentDateFormatted) {
      setIsDatePickerOpen(false);
      return;
    }

    handleInputChange("lastRefillDate", formattedDate);
    setIsDatePickerOpen(false);
  };

  const handleSubmit = async () => {
    const errors: typeof formErrors = {};

    if (!formData.ownerName.trim()) errors.ownerName = "Required";
    if (!formData.phone?.trim()) {
      errors.phone = "Required";
    } else if (cleanedPhone?.length !== 10) {
      errors.phone = "Phone number must be 10 digits";
    }
    if (!formData.petName.trim()) errors.petName = "Required";
    if (!formData.petType) errors.petType = "Required";
    if (!formData.petGender) errors.petGender = "Required";
    if (!formData.prescriptionName.trim()) errors.prescriptionName = "Required";

    setFormErrors(errors);
    if (Object.keys(errors).length > 0) return;

    setLoading(true);

    const modifiedSubmitData = {
      ...formData,
      //   calendarId: CalendarID,
      //   providerId: ProviderID,
      phone: `${selectedCountry?.dialCode}${cleanedPhone}`,
      lastRefillDate: formData.lastRefillDate
        ? dayjs(formData.lastRefillDate).format("YYYY-MM-DD")
        : undefined,
    };
    console.log(modifiedSubmitData, "modifiedSubmitData");

    // const response = await postContactSendData(
    //     modifiedSubmitData,
    //     API_CONSTANTS?.sendContactEmail?.contactEmail
    // );

    // if (response?.status === 200) {
    //     notify(response?.data?.message, "success");
    //     setFormData({
    //         ownerName: "",
    //         phone: "",
    //         petName: "",
    //         petType: "",
    //         petGender: "",
    //         prescriptionName: "",
    //         lastRefillDate: undefined,
    //         veterinarian: "",
    //         additionalNotes: "",
    //     });

    // } else {
    //     notify(response?.response?.data?.message, "error");
    // }

    // Simulate API call
    setTimeout(() => {
      setLoading(false);
      setSuccess(true);

      // Reset form
      setFormData({
        ownerName: "",
        phone: "",
        petName: "",
        petType: "",
        petGender: "",
        prescriptionName: "",
        lastRefillDate: undefined,
        veterinarian: "",
        additionalNotes: "",
      });

      // Hide success message after 3 seconds
      setTimeout(() => setSuccess(false), 3000);
    }, 2000);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br bg-[#f0f8ff] mt-10">
      {/* Header */}
      {/* <div className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-4">
            <div className="flex items-center space-x-3">
              <div className="w-12 h-12 bg-blue-600 rounded-lg flex items-center justify-center">
                <PawPrint className="w-6 h-6 text-white" />
              </div>
              <div>
                <h1 className="text-xl font-bold text-gray-900">Veterinary Medical Center</h1>
              </div>
            </div>
            <div className="hidden md:flex items-center space-x-6 text-sm text-gray-600">
              <div className="flex items-center space-x-1">
                <Phone className="w-4 h-4" />
                <span>(*************</span>
              </div>
              <div className="flex items-center space-x-1">
                <MapPin className="w-4 h-4" />
                <span>2765 Del Paso Rd 120, Sacramento, CA 95835 US</span>
              </div>
            </div>
          </div>
        </div>
      </div> */}

      {/* Main Content */}
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* <div className="text-center mb-8">
                    <div className="flex items-center justify-center space-x-2 mb-4">
                        <Pill className="w-8 h-8 text-blue-600" />
                        <h2 className="text-2xl font-bold text-gray-900">Prescription Refill Request</h2>
                    </div>
                    <p className="text-md text-gray-600 max-w-2xl mx-auto">
                        Request a prescription refill for your pet quickly and easily. We'll process your request and have it ready
                        for pickup or delivery.
                    </p>
                </div> */}

        <Card className="shadow-lg pt-0">
          <CardHeader className="bg-gradient-to-br bg-[#daeeff] rounded-t-lg py-6">
            <CardTitle className="text-2xl">
              Prescription Refill Request
            </CardTitle>
            <CardDescription className="">
              Please fill out all required fields marked with an asterisk (<span className="text-red-500"> * </span>)
            </CardDescription>
          </CardHeader>
          <CardContent className="p-6">
            <form onSubmit={(e) => e.preventDefault()} className="space-y-6">
              {/* Owner Information */}
              <div className="space-y-4">
                <div className="flex items-center space-x-2 mb-4">
                  <Badge
                    variant="outline"
                    className="text-blue-600 border-blue-600"
                  >
                    Owner & Pet Information
                  </Badge>
                </div>
                <p className="text-sm text-gray-600 mb-4">
                  Tell us about you and your pet
                </p>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label
                      htmlFor="ownerName"
                      className="flex items-center space-x-1"
                    >
                      <span>Owner Name</span>
                      <span className="text-red-500">*</span>
                      {formErrors.ownerName && (
                        <span className="inline-flex items-center rounded-md bg-red-50 px-2 py-1 text-xs font-medium text-red-700 ring-1 ring-red-600/10 ring-inset">
                          <span className="text-red-500 text-[12px]">
                            {formErrors.ownerName}
                          </span>
                        </span>
                      )}
                    </Label>
                    <Input
                      id="ownerName"
                      value={formData.ownerName}
                      onChange={(e) =>
                        handleInputChange("ownerName", e.target.value)
                      }
                      placeholder="Enter Owner Name"
                      required
                    />
                  </div>
                  <div className="space-y-2">
                    <Label
                      htmlFor="phone"
                      className="flex items-center space-x-1"
                    >
                      <Phone className="w-4 h-4" />
                      <span>Phone Number</span>
                      <span className="text-red-500">*</span>
                      {formErrors.phone && (
                        <span className="inline-flex items-center rounded-md bg-red-50 px-2 py-1 text-xs font-medium text-red-700 ring-1 ring-red-600/10 ring-inset">
                          <span className="text-red-500 text-[12px]">
                            {formErrors.phone}
                          </span>
                        </span>
                      )}
                    </Label>
                    <Input
                      id="phone"
                      type="tel"
                      value={formData.phone}
                      onChange={(e) => {
                        const raw = e.target.value.replace(/\D/g, ""); // Remove non-digits
                        let formatted = raw;

                        if (raw.length > 0) {
                          formatted = `(${raw.slice(0, 3)}`;
                        }
                        if (raw.length >= 4) {
                          formatted += `) ${raw.slice(3, 6)}`;
                        }
                        if (raw.length >= 7) {
                          formatted += `-${raw.slice(6, 10)}`;
                        }

                        handleInputChange("phone", formatted);
                      }}
                      placeholder="(*************"
                      required
                    />
                  </div>
                </div>
              </div>

              {/* Pet Information */}
              <div className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label
                      htmlFor="petName"
                      className="flex items-center space-x-1"
                    >
                      <PawPrint className="w-4 h-4" />
                      <span>Pet Name</span>
                      <span className="text-red-500">*</span>
                      {formErrors.petName && (
                        <span className="inline-flex items-center rounded-md bg-red-50 px-2 py-1 text-xs font-medium text-red-700 ring-1 ring-red-600/10 ring-inset">
                          <span className="text-red-500 text-[12px]">
                            {formErrors.petName}
                          </span>
                        </span>
                      )}
                    </Label>
                    <Input
                      id="petName"
                      value={formData.petName}
                      onChange={(e) =>
                        handleInputChange("petName", e.target.value)
                      }
                      placeholder="Enter Pet Name"
                      required
                    />
                  </div>
                  <div className="space-y-2">
                    <Label className="flex items-center space-x-1">
                      <span>Pet Type</span>
                      <span className="text-red-500">*</span>
                      {formErrors.petType && (
                        <span className="inline-flex items-center rounded-md bg-red-50 px-2 py-1 text-xs font-medium text-red-700 ring-1 ring-red-600/10 ring-inset">
                          <span className="text-red-500 text-[12px]">
                            {formErrors.petType}
                          </span>
                        </span>
                      )}
                    </Label>
                    <div className="flex space-x-3">
                      <Button
                        type="button"
                        variant={
                          formData.petType === "CAN" ? "default" : "outline"
                        }
                        className={`flex-1 h-14 sm:h-16  flex flex-col items-center justify-center gap-1 transition-all text-sm sm:text-base  ${
                          formData.petType === "CAN"
                            ? "bg-blue-600 hover:bg-blue-700 text-white"
                            : "hover:bg-blue-50 hover:border-blue-300"
                        }`}
                        onClick={() => handleInputChange("petType", "CAN")}
                      >
                        <Image
                          src="/images/dog_profile.png"
                          alt="Dog"
                          width={26}
                          height={26}
                          className="rounded-full object-cover"
                          title="Dog"
                        />
                        <span className="text-sm">Dog</span>
                      </Button>
                      <Button
                        type="button"
                        variant={
                          formData.petType === "FEL" ? "default" : "outline"
                        }
                        className={`flex-1 h-14 sm:h-16  flex flex-col items-center justify-center gap-1 transition-all text-sm sm:text-base  ${
                          formData.petType === "FEL"
                            ? "bg-blue-600 hover:bg-blue-700 text-white"
                            : "hover:bg-blue-50 hover:border-blue-300"
                        }`}
                        onClick={() => handleInputChange("petType", "FEL")}
                      >
                        <Image
                          src="/images/cat-8570981.webp"
                          alt="Cat"
                          width={30}
                          height={30}
                          className="rounded-full object-cover"
                          title="Cat"
                        />
                        <span className="text-sm">Cat</span>
                      </Button>
                    </div>
                  </div>
                </div>

                <div className="space-y-2">
                  <Label className="flex items-center space-x-1">
                    <span>Pet Gender</span>
                    <span className="text-red-500">*</span>
                    {formErrors.petGender && (
                      <span className="inline-flex items-center rounded-md bg-red-50 px-2 py-1 text-xs font-medium text-red-700 ring-1 ring-red-600/10 ring-inset">
                        <span className="text-red-500 text-[12px]">
                          {formErrors.petGender}
                        </span>
                      </span>
                    )}
                  </Label>
                  <div className="flex space-x-3">
                    <Button
                      type="button"
                      variant={
                        formData.petGender === "male" ? "default" : "outline"
                      }
                      className={`flex-1 ${
                        formData.petGender === "male"
                          ? "bg-blue-600 hover:bg-blue-700 text-white"
                          : "hover:bg-blue-50 hover:border-blue-300"
                      }`}
                      onClick={() => handleInputChange("petGender", "male")}
                    >
                      Male
                    </Button>
                    <Button
                      type="button"
                      variant={
                        formData.petGender === "female" ? "default" : "outline"
                      }
                      className={`flex-1 ${
                        formData.petGender === "female"
                          ? "bg-blue-600 hover:bg-blue-700 text-white"
                          : "hover:bg-blue-50 hover:border-blue-300"
                      }`}
                      onClick={() => handleInputChange("petGender", "female")}
                    >
                      Female
                    </Button>
                  </div>
                </div>
              </div>

              {/* Prescription Information */}
              <div className="space-y-4">
                <div className="flex items-center space-x-2 mb-4">
                  <Badge
                    variant="outline"
                    className="text-purple-600 border-purple-600"
                  >
                    Prescription Details
                  </Badge>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label
                      htmlFor="prescriptionName"
                      className="flex items-center space-x-1"
                    >
                      <span>Prescription/Medication Name </span>
                      <span className="text-red-500">*</span>

                      {formErrors.prescriptionName && (
                        <span className="inline-flex items-center rounded-md bg-red-50 px-2 py-1 text-xs font-medium text-red-700 ring-1 ring-red-600/10 ring-inset">
                          <span className="text-red-500 text-[12px]">
                            {formErrors.prescriptionName}
                          </span>
                        </span>
                      )}
                    </Label>
                    <Input
                      id="prescriptionName"
                      value={formData.prescriptionName}
                      onChange={(e) =>
                        handleInputChange("prescriptionName", e.target.value)
                      }
                      placeholder="Enter medication name"
                      required
                    />
                  </div>
                  {/* <div className="space-y-2">
                                        <Label htmlFor="lastRefillDate">Last Refill Date</Label>
                                        <Popover open={isDatePickerOpen} onOpenChange={setIsDatePickerOpen}>
                                            <PopoverTrigger asChild>
                                                <Button
                                                    variant="outline"
                                                    className="w-full justify-start text-left font-normal bg-transparent"
                                                    type="button"
                                                >
                                                    <CalendarIcon className="mr-2 h-4 w-4" />
                                                    {formData.lastRefillDate ? (
                                                        format(formData.lastRefillDate, "MM/dd/yyyy")
                                                    ) : (
                                                        <span className="text-muted-foreground">Select date</span>
                                                    )}
                                                </Button>
                                            </PopoverTrigger>
                                            <PopoverContent className="w-auto p-0" align="start">
                                                <Calendar
                                                    mode="single"
                                                    selected={
                                                        formData.lastRefillDate
                                                            ? dayjs(formData.lastRefillDate)
                                                                .tz(userTimezone)
                                                                .toDate()
                                                            : undefined
                                                    }
                                                    onSelect={handleLastRefillSelect}
                                                    disabled={(date) => date > new Date() || date < new Date("1900-01-01")}
                                                />
                                            </PopoverContent>
                                        </Popover>
                                    </div> */}
                  <div className="space-y-2">
                    <Label htmlFor="lastRefillDate">Last Refill Date</Label>

                    <Popover
                      open={isDatePickerOpen}
                      onOpenChange={setIsDatePickerOpen}
                    >
                      <div className="relative">
                        <PopoverTrigger asChild>
                          <Button
                            variant="outline"
                            className="w-full justify-start text-left font-normal bg-transparent pr-10"
                            type="button"
                          >
                            <CalendarIcon className="mr-2 h-4 w-4" />
                            {formData.lastRefillDate ? (
                              format(formData.lastRefillDate, "MM/dd/yyyy")
                            ) : (
                              <span className="text-muted-foreground">
                                Select date
                              </span>
                            )}
                          </Button>
                        </PopoverTrigger>

                        {formData.lastRefillDate && (
                          <button
                            type="button"
                            onClick={(e) => {
                              e.stopPropagation(); // Prevent popover toggle
                              handleInputChange("lastRefillDate", ""); // Clear the date
                              setIsDatePickerOpen(false); // Close calendar
                            }}
                            className="absolute right-2 top-1/2 -translate-y-1/2 text-gray-400 hover:text-gray-600 z-10"
                          >
                            <X className="w-4 h-4" />
                          </button>
                        )}
                      </div>

                      <PopoverContent className="w-auto p-0" align="start">
                        <Calendar
                          mode="single"
                          selected={
                            formData.lastRefillDate
                              ? dayjs(formData.lastRefillDate)
                                  .tz(userTimezone)
                                  .toDate()
                              : undefined
                          }
                          onSelect={handleLastRefillSelect}
                          disabled={(date) =>
                            date > new Date() || date < new Date("1900-01-01")
                          }
                        />
                      </PopoverContent>
                    </Popover>
                  </div>
                </div>

                {/* <div className="space-y-2">
                                    <Label htmlFor="veterinarian">Prescribing Veterinarian</Label>
                                    <Select
                                        value={formData.veterinarian}
                                        onValueChange={(value) => handleInputChange("veterinarian", value)}
                                    >
                                        <SelectTrigger>
                                            <SelectValue placeholder="Select veterinarian (if known)" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            <SelectItem value="Dr. Amandeep Singh">Dr. Amandeep Singh</SelectItem>
                                            <SelectItem value="Dr. Shahid Zaigham">Dr. Shahid Zaigham</SelectItem>
                                            <SelectItem value="Dr. Umer Khan">Dr. Umer Khan</SelectItem>
                                            <SelectItem value="Antonio">Dr. Antonio</SelectItem>
                                            <SelectItem value="Jorge">Dr. Jorge</SelectItem>
                                            <SelectItem value="Alexia">Dr. Alexia</SelectItem>
                                            <SelectItem value="unknown">Not sure/Other</SelectItem>
                                        </SelectContent>
                                    </Select>
                                </div> */}
                <div className="space-y-2">
                  <Label htmlFor="veterinarian">Prescribing Veterinarian</Label>

                  <div className="relative">
                    <Select
                      value={formData.veterinarian}
                      onValueChange={(value) =>
                        handleInputChange("veterinarian", value)
                      }
                    >
                      <SelectTrigger className="pr-6">
                        {" "}
                        {/* give space for icon */}
                        <SelectValue placeholder="Select veterinarian (if known)" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="Dr. Amandeep Singh">
                          Dr. Amandeep Singh
                        </SelectItem>
                        <SelectItem value="Dr. Shahid Zaigham">
                          Dr. Shahid Zaigham
                        </SelectItem>
                        <SelectItem value="Dr. Umer Khan">
                          Dr. Umer Khan
                        </SelectItem>
                        <SelectItem value="Antonio">Dr. Antonio</SelectItem>
                        <SelectItem value="Jorge">Dr. Jorge</SelectItem>
                        <SelectItem value="Alexia">Dr. Alexia</SelectItem>
                        <SelectItem value="unknown">Not sure/Other</SelectItem>
                      </SelectContent>
                    </Select>

                    {/* Clear (X) button */}
                    {formData.veterinarian && (
                      <button
                        type="button"
                        onClick={(e) => {
                          e.stopPropagation(); // prevent dropdown open
                          handleInputChange("veterinarian", ""); // clear value

                          // Optional: blur to close dropdown if open
                          // const el = e.currentTarget.closest("button")?.blur();
                        }}
                        className="absolute right-2 top-1/2 -translate-y-1/2 text-gray-400 hover:text-gray-600"
                      >
                        <X className="w-4 h-4" /> {/* Use an icon or '✕' */}
                      </button>
                    )}
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="additionalNotes">Additional Notes</Label>
                  <Textarea
                    id="additionalNotes"
                    value={formData.additionalNotes}
                    onChange={(e) =>
                      handleInputChange("additionalNotes", e.target.value)
                    }
                    placeholder="Any additional information about the prescription or special instructions..."
                    rows={3}
                  />
                </div>
              </div>

              {/* Processing Information */}
              <div className="bg-blue-50 p-4 rounded-lg">
                <div className="flex items-start space-x-3">
                  <Clock className="w-5 h-5 text-blue-600 mt-0.5" />
                  <div>
                    <h4 className="font-semibold text-blue-900 mb-1">
                      Processing Time
                    </h4>
                    <p className="text-sm text-blue-700">
                      Prescription refill requests are typically processed
                      within 24-48 hours during business hours. We&apos;ll
                      contact you using your preferred method when your
                      prescription is ready for pickup or delivery.
                    </p>
                  </div>
                </div>
              </div>

              {success && (
                <div className="mb-4 text-green-700 bg-green-100 border border-green-300 rounded p-2 text-center">
                  Prescription refill request submitted successfully!
                </div>
              )}

              <Button
                type="button"
                disabled={loading}
                onClick={handleSubmit}
                className="w-full border-2 border-[#0274D9] rounded-full bg-[#0274D9] transition-all hover:bg-white hover:text-[#0274D9] text-white py-3 text-md cursor-pointer"
              >
                {loading && (
                  <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin" />
                )}
                {loading ? "Submitting..." : "Submit Refill Request"}
              </Button>
            </form>
          </CardContent>
        </Card>

        {/* Contact Information */}
        <div className="mt-8 text-center">
          <p className="text-gray-600 mb-4">
            Questions about your prescription refill? Contact us directly:
          </p>
          <div className="flex flex-col sm:flex-row justify-center items-center space-y-2 sm:space-y-0 sm:space-x-6">
            <div className="flex items-center space-x-2 text-gray-700">
              <Phone className="w-4 h-4" />
              <a href="tel:+14083725811" className="hover:underline">
                (*************
              </a>
            </div>
            <div className="flex items-center space-x-2 text-gray-700">
              <Mail className="w-4 h-4" />
              <a href="mailto:<EMAIL>" className="hover:underline">
                <EMAIL>
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
