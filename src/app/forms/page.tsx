// app/forms/page.tsx
"use client";
import HoverButton from "@/components/common/hoverButton";
import { UserPen } from "lucide-react";
import Image from "next/image";

export default function FormsPage() {
  const cards = [
    {
      title: "RX Refill Form",
      description:
        "Easily request your prescription refill anytime — quick, secure, and convenient.",
      icon: <UserPen className="text-white text-4xl mb-4" />,
      link: "/forms/prescription-refill-request",
      buttonText: "Visit Form",
      image:
        "https://img.freepik.com/free-photo/view-3d-document-lawyer-s-day-celebration_23-**********.jpg",
    },
    {
      title: "Food Order Form",
      description:
        "Get food with ease using our Food Order Form — simple, fast, and reliable.",
      icon: <UserPen className="text-white text-4xl mb-4" />,
      link: "/forms/food-order-form",
      buttonText: "Visit Form",
      image:
        "https://img.freepik.com/free-photo/view-3d-document-lawyer-s-day-celebration_23-**********.jpg",
    },
    {
      title: "Various Consent Forms",
      description:
        "Access a range of customizable consent forms — safe, compliant, and built for your needs.",
      icon: <UserPen className="text-white text-4xl mb-4" />,
      link: "/forms/consent-forms",
      buttonText: "Visit Forms",
      image: "https://img.freepik.com/free-photo/view-3d-document-lawyer-s-day-celebration_23-**********.jpg", // Example Unsplash link
    },
    {
      title: "New Patient Form",
      description:
        "Smooth onboarding with our New Patient Form — quick to complete, secure, and ready for your first visit.",
      icon: <UserPen className="text-white text-4xl mb-4" />,
      link: "/forms/new-patient",
      buttonText: "Visit Form",
      image: "https://img.freepik.com/free-photo/view-3d-document-lawyer-s-day-celebration_23-**********.jpg", // Example Unsplash link
    },
  ];

  return (
    <div className=" bg-gray-100 px-4 py-24">
      {/* Heading Section */}
      <div className="max-w-3xl mx-auto text-center mb-15">
        <h1 className="text-3xl md:text-4xl font-semibold text-gray-900">
          Medical Forms Made Easy
        </h1>
        <p className="text-gray-600 mt-3">
          Create custom medical forms with us — fast, reliable, and tailored for
          your practice
        </p>
      </div>

      {/* Cards Section */}
      <div className="max-w-5xl mx-auto grid grid-cols-1 md:grid-cols-2 gap-8">
        {cards.map((card, index) => (
          <div
            key={index}
            className="relative rounded-2xl overflow-hidden shadow-lg group h-[18rem] hover:scale-105 transition-transform duration-300"
          >
            {/* Background Image with Blur */}
            <div className="absolute inset-0">
              <Image
                src={card.image}
                alt={card.title}
                fill
                className="object-cover blur-md group-hover:blur-md transition-all duration-300"
                priority
              />
            </div>

            {/* Overlay */}
            <div className="absolute inset-0 bg-gradient-to-t from-black/70 via-black/40 to-transparent"></div>

            {/* Content */}
            <div className="relative z-10 flex flex-col items-center justify-end h-full p-6 text-center text-white">
              {card.icon}
              <h2 className="text-2xl font-bold mb-2">{card.title}</h2>
              <p className="text-sm mb-6">{card.description}</p>
              {/* <HoverButton
                text={card.buttonText}
                link={card.link}
                className=""
              /> */}
            </div>
          </div>
        ))}
      </div>
      <div className="text-center mt-12">
        <HoverButton text={"Book Demo"} link={"/contact"} className="" />
      </div>
    </div>
  );
}
