/* eslint-disable @typescript-eslint/no-explicit-any */

"use client";
import { But<PERSON> } from "@/components/ui/button";
import { Calendar } from "@/components/ui/calendar";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { countryPhoneCodes } from "@/constants/common-constant";
import { cleanPhoneNumber } from "@/constants/dateTime";
import { format } from "date-fns";
import dayjs from "dayjs";
import timezone from "dayjs/plugin/timezone";
import utc from "dayjs/plugin/utc";
import { CalendarIcon, PawPrint, Phone, X } from "lucide-react";
import Image from "next/image";
import { useState } from "react";
import { Control, Controller, FieldErrors, useForm } from "react-hook-form";
dayjs.extend(utc);
dayjs.extend(timezone);

interface InfoSectionProps {
  title?: string;
  content?: string;
}

interface ConsentSectionProps {
  label: string;
  field: string;
  register: any;
  errors: any;
  isRequired?: boolean;
  options?: { label: string; value: string }[];
}

interface DropdownOption {
  label: string;
  value: string;
}
interface DropdownSelectProps {
  name: string;
  label?: string;
  placeholder?: string;
  options: DropdownOption[];
  control: Control<any>;
  errors?: FieldErrors;
  required?: boolean;
  clearable?: boolean;
}

interface FormDataType {
  surgeryDate: Date | null;
  ownerName?: string;
  petName?: string;
  primaryphone?: string;
  alternatephone?: string;
  licenceNumber?: string;
  email?: string;
  toeNailTrim?: string;
  heartwormTest?: string;
  microchip?: string;
  earFlushing?: string;
  fecal?: string;
}
const SurgicalConsentForm = () => {
  const [isDatePickerOpen, setIsDatePickerOpen] = useState(false);
  const userTimezone = "America/Los_Angeles";
  const [selectedCountry] = useState(countryPhoneCodes[0]);

  const [loading, setLoading] = useState(false);
  const [success, setSuccess] = useState(false);

  //  const { postData: postContactSendData, isLoading: postContactAPILoading } =
  //     usePostApi("");
  //   const { notify } = useNotificationContext();

  const {
    control,
    register,
    handleSubmit,
    formState: { errors },
    reset,
  } = useForm<FormDataType | any>({
    defaultValues: {
      surgeryDate: null,
      primaryphone: "",
      alternatephone: "",
      email: "",
      toeNailTrim: "",
      heartwormTest: "",
      microchip: "",
      earFlushing: "",
      fecal: "",
    },
  });

  const onSubmit = async (data: any) => {
    setLoading(true);
    const modifiedSubmitData = {
      ...data,
      primaryphone: `${selectedCountry?.dialCode}${cleanPhoneNumber(
        data?.primaryphone
      )}`,
      alternatephone: data?.alternatephone
        ? `${selectedCountry?.dialCode}${cleanPhoneNumber(
            data?.alternatephone
          )}`
        : undefined,
      surgeryDate: data?.surgeryDate
        ? dayjs(data?.surgeryDate).format("YYYY-MM-DD")
        : undefined,
    };

    console.log(modifiedSubmitData, "modifiedSubmitData");

    //  const response = await postContactSendData(
    //          modifiedSubmitData,
    //           API_CONSTANTS?.sendContactEmail?.contactEmail
    //         );
    //  if (response?.status === 200) {
    //   notify(response?.data?.message, "success");
    //     reset();

    // } else {
    //   notify(response?.response?.data?.message, "error");
    // }

    // Simulate API call
    setTimeout(() => {
      setLoading(false);
      setSuccess(true);

      // Reset form
      reset();

      // Hide success message after 3 seconds
      setTimeout(() => setSuccess(false), 3000);
    }, 2000);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br bg-[#f0f8ff] mt-10">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <Card className="shadow-lg pt-0">
          <CardHeader className="bg-gradient-to-br bg-[#daeeff] rounded-t-lg py-6">
            <CardTitle className="text-2xl">Surgical Consent Form</CardTitle>
            <CardDescription className="">
              Please fill out all required fields marked with an asterisk (<span className="text-red-500"> * </span>)
            </CardDescription>
          </CardHeader>

          <CardContent className="p-6">
            <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
              {/* Surgery Date */}
              <div>
                <InfoSection title="PLEASE ALLOW 48 HOURS FOR PROCESSING" />
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="surgeryDate">Surgery Date</Label>

                    <Controller
                      control={control}
                      name="surgeryDate"
                      rules={{}}
                      render={({ field: { onChange, value } }) => (
                        <Popover
                          open={isDatePickerOpen}
                          onOpenChange={setIsDatePickerOpen}
                        >
                          <div className="relative">
                            <PopoverTrigger asChild>
                              <Button
                                variant="outline"
                                className="w-full justify-start text-left font-normal bg-transparent pr-10"
                                type="button"
                              >
                                <CalendarIcon className="mr-2 h-4 w-4" />
                                {value ? (
                                  format(value, "MM/dd/yyyy")
                                ) : (
                                  // dayjs(value).tz(userTimezone)?.format("MM/DD/YYYY")
                                  <span className="text-muted-foreground">
                                    Select date
                                  </span>
                                )}
                              </Button>
                            </PopoverTrigger>

                            {value && (
                              <button
                                type="button"
                                onClick={(e) => {
                                  e.stopPropagation();
                                  onChange(null); // Clear the value
                                  setIsDatePickerOpen(false);
                                }}
                                className="absolute right-2 top-1/2 -translate-y-1/2 text-gray-400 hover:text-gray-600 z-10"
                              >
                                <X className="w-4 h-4" />
                              </button>
                            )}
                          </div>

                          <PopoverContent className="w-auto p-0" align="start">
                            <Calendar
                              mode="single"
                              // selected={
                              //     value
                              //         ? dayjs(value)
                              //             .tz(userTimezone)
                              //             .startOf("day")
                              //             .toDate()
                              //         : undefined
                              // }
                              selected={
                                value
                                  ? dayjs(value).startOf("day").toDate()
                                  : undefined
                              }
                              onSelect={(selectedDate) => {
                                onChange(selectedDate);
                                setIsDatePickerOpen(false);
                              }}
                              disabled={(date) => {
                                const todayPST = dayjs()
                                  .tz(userTimezone)
                                  .startOf("day");

                                const formattedDate =
                                  dayjs(date).format("YYYY-MM-DD");
                                const datePST = dayjs.tz(
                                  formattedDate,
                                  "YYYY-MM-DD",
                                  userTimezone
                                );

                                return datePST.isBefore(todayPST);
                              }}
                            />
                          </PopoverContent>
                        </Popover>
                      )}
                    />
                    {/* 
                                    {typeof errors?.surgeryDate?.message === "string" && (
                                        <p className="text-red-500 text-sm">{errors.surgeryDate.message}</p>
                                    )} */}
                  </div>
                </div>
              </div>
              {/* Surgical Consent Form */}
              <div className="space-y-4">
                <InfoSection title="Surgical Consent Form" />
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {/* Owner Name< */}
                  <div className="space-y-2">
                    <Label
                      htmlFor="ownerName"
                      className="flex items-center space-x-1"
                    >
                      <span>Owner Name</span>
                      <span className="text-red-500">*</span>
                      {errors.ownerName && (
                        <span className="inline-flex items-center rounded-md bg-red-50 px-2 py-1 text-xs font-medium text-red-700 ring-1 ring-red-600/10 ring-inset">
                          <span className="text-red-500 text-[12px]">
                            {errors.ownerName.message?.toString() || "Required"}
                          </span>
                        </span>
                      )}
                    </Label>

                    <Input
                      id="ownerName"
                      placeholder="Enter Owner Name"
                      {...register("ownerName", {
                        required: "Required",
                      })}
                    />
                  </div>
                  {/* petName */}
                  <div className="space-y-2">
                    <Label
                      htmlFor="petName"
                      className="flex items-center space-x-1"
                    >
                      <PawPrint className="w-4 h-4" />
                      <span>Pet Name</span>
                      <span className="text-red-500">*</span>
                      {errors.petName && (
                        <span className="inline-flex items-center rounded-md bg-red-50 px-2 py-1 text-xs font-medium text-red-700 ring-1 ring-red-600/10 ring-inset">
                          <span className="text-red-500 text-[12px]">
                            {errors.petName.message?.toString() || "Required"}
                          </span>
                        </span>
                      )}
                    </Label>
                    <Input
                      id="petName"
                      placeholder="Enter Pet Name"
                      {...register("petName", {
                        required: "Required",
                      })}
                    />
                  </div>
                </div>

                <Controller
                  name="petType"
                  control={control}
                  rules={{ required: "Required" }}
                  render={({
                    field: { value, onChange },
                    fieldState: { error },
                  }) => (
                    <div className="space-y-2">
                      <Label className="flex items-center space-x-1">
                        <span>Pet Type</span>
                        <span className="text-red-500">*</span>
                        {error && (
                          <span className="inline-flex items-center rounded-md bg-red-50 px-2 py-1 text-xs font-medium text-red-700 ring-1 ring-red-600/10 ring-inset">
                            <span className="text-red-500 text-[12px]">
                              {error.message}
                            </span>
                          </span>
                        )}
                      </Label>

                      <div className="flex space-x-3">
                        {/* DOG button */}
                        <Button
                          type="button"
                          variant={value === "CAN" ? "default" : "outline"}
                          className={`flex-1 h-14 sm:h-16 flex flex-col items-center justify-center gap-1 text-sm sm:text-base transition-all ${
                            value === "CAN"
                              ? "bg-blue-600 hover:bg-blue-700 text-white"
                              : "hover:bg-blue-50 hover:border-blue-300"
                          }`}
                          onClick={() => onChange("CAN")}
                        >
                          <Image
                            src="/images/dog_profile.png"
                            alt="Dog"
                            width={26}
                            height={26}
                            className="rounded-full object-cover"
                            title="Dog"
                          />
                          <span className="text-sm">Dog</span>
                        </Button>

                        {/* CAT button */}
                        <Button
                          type="button"
                          variant={value === "FEL" ? "default" : "outline"}
                          className={`flex-1 h-14 sm:h-16 flex flex-col items-center justify-center gap-1 text-sm sm:text-base transition-all ${
                            value === "FEL"
                              ? "bg-blue-600 hover:bg-blue-700 text-white"
                              : "hover:bg-blue-50 hover:border-blue-300"
                          }`}
                          onClick={() => onChange("FEL")}
                        >
                          <Image
                            src="/images/cat-8570981.webp"
                            alt="Cat"
                            width={30}
                            height={30}
                            className="rounded-full object-cover"
                            title="Cat"
                          />
                          <span className="text-sm">Cat</span>
                        </Button>
                      </div>
                    </div>
                  )}
                />

                {/* Primary Phone  */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Controller
                      control={control}
                      name="primaryphone"
                      rules={{
                        required: "Required",
                        pattern: {
                          value: /^\(\d{3}\) \d{3}-\d{4}$/,
                          message: "Phone number must be 10 digits",
                        },
                      }}
                      render={({
                        field: { onChange, value, ...field },
                        fieldState: { error },
                      }) => (
                        <div className="space-y-2">
                          <Label
                            htmlFor="primaryphone"
                            className="flex items-center space-x-1"
                          >
                            <Phone className="w-4 h-4" />
                            <span>Phone Number</span>
                            <span className="text-red-500">*</span>
                            {error && (
                              <span className="inline-flex items-center rounded-md bg-red-50 px-2 py-1 text-xs font-medium text-red-700 ring-1 ring-red-600/10 ring-inset">
                                <span className="text-red-500 text-[12px]">
                                  {error.message}
                                </span>
                              </span>
                            )}
                          </Label>

                          <Input
                            id="primaryphone"
                            type="tel"
                            placeholder="(*************"
                            value={value ?? ""}
                            onChange={(e) => {
                              const raw = e.target.value.replace(/\D/g, ""); // Remove non-digits
                              let formatted = raw;

                              if (raw.length > 0) {
                                formatted = `(${raw.slice(0, 3)}`;
                              }
                              if (raw.length >= 4) {
                                formatted += `) ${raw.slice(3, 6)}`;
                              }
                              if (raw.length >= 7) {
                                formatted += `-${raw.slice(6, 10)}`;
                              }

                              onChange(formatted);
                            }}
                            {...field}
                          />
                        </div>
                      )}
                    />
                  </div>

                  {/* alternate phone number  */}
                  <div className="space-y-2">
                    <Controller
                      control={control}
                      name="alternatephone"
                      rules={{
                        // required: "Required",
                        pattern: {
                          value: /^\(\d{3}\) \d{3}-\d{4}$/,
                          message: "Phone number must be 10 digits",
                        },
                      }}
                      render={({
                        field: { onChange, value, ...field },
                        fieldState: { error },
                      }) => (
                        <div className="space-y-2">
                          <Label
                            htmlFor="alternatephone"
                            className="flex items-center space-x-1"
                          >
                            <Phone className="w-4 h-4" />
                            <span>Alternate Phone Number</span>
                            {/* <span className="text-red-500">*</span> */}
                            {error && (
                              <span className="inline-flex items-center rounded-md bg-red-50 px-2 py-1 text-xs font-medium text-red-700 ring-1 ring-red-600/10 ring-inset">
                                <span className="text-red-500 text-[12px]">
                                  {error.message}
                                </span>
                              </span>
                            )}
                          </Label>

                          <Input
                            id="alternatephone"
                            type="tel"
                            placeholder="(*************"
                            value={value ?? ""}
                            onChange={(e) => {
                              const raw = e.target.value.replace(/\D/g, ""); // Remove non-digits
                              let formatted = raw;

                              if (raw.length > 0) {
                                formatted = `(${raw.slice(0, 3)}`;
                              }
                              if (raw.length >= 4) {
                                formatted += `) ${raw.slice(3, 6)}`;
                              }
                              if (raw.length >= 7) {
                                formatted += `-${raw.slice(6, 10)}`;
                              }

                              onChange(formatted);
                            }}
                            {...field}
                          />
                        </div>
                      )}
                    />
                  </div>
                  {/* Drivers License Number */}
                  <div className="space-y-2">
                    <Label
                      htmlFor="licenceNumber"
                      className="flex items-center space-x-1"
                    >
                      <span>Drivers License Number</span>
                      {/* <span className="text-red-500">*</span> */}
                      {errors.licenceNumber && (
                        <span className="inline-flex items-center rounded-md bg-red-50 px-2 py-1 text-xs font-medium text-red-700 ring-1 ring-red-600/10 ring-inset">
                          <span className="text-red-500 text-[12px]">
                            {errors.licenceNumber.message?.toString() ||
                              "Required"}
                          </span>
                        </span>
                      )}
                    </Label>
                    <Input
                      id="licenceNumber"
                      placeholder="Enter  Drivers License Number"
                      {...register("licenceNumber", {
                        // required: "Required",
                      })}
                    />
                  </div>
                  {/* Email Address */}
                  <div className="space-y-2">
                    <Label
                      htmlFor="email"
                      className="flex items-center space-x-1"
                    >
                      <span>Email</span>
                      {errors.email && (
                        <span className="inline-flex items-center rounded-md bg-red-50 px-2 py-1 text-xs font-medium text-red-700 ring-1 ring-red-600/10 ring-inset">
                          <span className="text-red-500 text-[12px]">
                            {errors.email.message?.toString() ||
                              "Invalid email"}
                          </span>
                        </span>
                      )}
                    </Label>

                    <Input
                      id="email"
                      type="email"
                      placeholder="Enter Email Address"
                      {...register("email", {
                        pattern: {
                          value: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
                          message: "Please enter a valid email address",
                        },
                      })}
                    />
                  </div>
                </div>

                <InfoSection content="The quality of care expected in Veterinary Medicine is comparable to that in human medicine. We HIGHLY RECOMMEND that the following preventative and preemptive procedures be preformed to allow the safest anesthesia risk possible. These procedures also allow for a quicker and more comfortable recovery. It is especially important for older or patients with preexisting medical conditions. However, we recognize that extenuating circumstances sometimes will not allow for this level of care, therefore you may decline any of the following by selecting the appropriate choice." />
              </div>

              <InfoSection
                title="Pre-Surgical Blood Screening"
                content="When your pet is admitted, rest assured that advances in anesthesia and surgery have made routine procedures relatively safe with a low rate of complications. However, since anesthesia or surgeries are not without risk, the American College of Veterinary Anesthesiology recommends that all animals have pre-surgical blood screening and EKG before anesthesia is administered. The pre-surgical blood screening includes a COMPLETE BLOOD COUNT (to evaluate for infection, anemia, or clotting deficiencies) and a CHEMISTRY CHECK (to evaluate liver and kidney enzymes - the filtering organs for anesthesia)."
              />

              {/* Pre-Surgical Options */}
              <ConsentSection
                label="I DO/DO NOT want my pet to have a pre-surgical blood screening"
                field="bloodScreen"
                register={register}
                errors={errors}
                isRequired={true}
                options={[
                  { label: "Do ($78.95)", value: "Do ($78.95)" },
                  { label: "Do Not", value: "Do Not" },
                ]}
              />

              <InfoSection
                title="Pre-Surgical EKG"
                content="An EKG is an electrical tracing of the heart's function. It is the standard that cardiologist's use to evaluate the heart. It shows changes in the heart long before they can be heard with a stethoscope."
              />
              <ConsentSection
                label="I DO/DO NOT want my pet to have a pre-surgical EKG"
                field="ekg"
                register={register}
                isRequired={true}
                errors={errors}
                options={[
                  { label: "Do ($49.76)", value: "Do ($49.76)" },
                  { label: "Do Not", value: "Do Not" },
                ]}
              />

              <InfoSection
                title="Vital Scan Monitoring"
                content="This is a multitude of sensors that are used to monitor your pet while it is anesthetized. It monitors heart rate, EKG, blood pressure, amount of oxygen in the blood, respirations, and temperature. These monitors allow us to make real time, split second decisions for your pet's well being."
              />

              <ConsentSection
                label="I DO/DO NOT want my pet to have vital scan monitoring"
                field="vitalScan"
                isRequired={true}
                register={register}
                errors={errors}
                options={[
                  { label: "Do ($39.76)", value: "Do ($39.76)" },
                  { label: "Do Not", value: "Do Not" },
                ]}
              />

              <InfoSection
                title="IV Catheter with Fluids"
                content="An intravenous catheter is placed to allow quick access to a vein in case of emergency or to sustain critical care. Fluids are also given during procedures to maintain blood pressure, correct dehydration, replace blood loss, and speed recover."
              />

              <ConsentSection
                label="I DO/DO NOT want my pet to have an IV Catheter and Fluids"
                isRequired={false}
                field="ivCatheter"
                register={register}
                errors={errors}
                options={[
                  { label: "Do ($47.11)", value: "Do ($47.11)" },
                  { label: "Do Not", value: "Do Not" },
                ]}
              />
              <InfoSection
                title="Laser"
                content="This process will help speed up your pets healing process time"
              />
              <ConsentSection
                label="I DO/DO NOT want the faster healing process"
                field="laser"
                isRequired={false}
                register={register}
                errors={errors}
                options={[
                  { label: "Do ($15)", value: "Do ($15)" },
                  { label: "Do Not", value: "Do Not" },
                ]}
              />

              <InfoSection title="COMPREHENSIVE CARE PACKAGE which includes all of the above pre-surgical and pain management options at a %15 savings." />

              <ConsentSection
                label="I DO/DO NOT want this savings"
                field="comprehensivePackage"
                register={register}
                isRequired={false}
                errors={errors}
                options={[
                  { label: "Do ($230.76)", value: "Do ($230.76)" },
                  { label: "Do Not", value: "Do Not" },
                ]}
              />

              <InfoSection
                title="Other Services"
                content="The following are optional services that are easily performed under sedation. Please check any services you DO WANT performed."
              />
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <DropdownSelect
                  name="toeNailTrim"
                  label="Toe Nail Trim"
                  placeholder="Select Toe Nail Trim"
                  control={control}
                  errors={errors}
                  options={[
                    { label: "I Do Not Want", value: "I Do Not Want" },
                    {
                      label: "I Do Want ($17.75)",
                      value: "I Do Want ($ 17.75)",
                    },
                  ]}
                  clearable
                  required={false}
                />

                <DropdownSelect
                  name="heartwormTest"
                  label="Heartworm Test"
                  placeholder="Select Heartworm Test"
                  control={control}
                  errors={errors}
                  options={[
                    { label: "I Do Not Want", value: "I Do Not Want" },
                    {
                      label: "I Do Want ($47.46)",
                      value: "I Do Want ($47.46)",
                    },
                  ]}
                  clearable
                  required={false}
                />
                <DropdownSelect
                  name="microchip"
                  label="Microchip"
                  placeholder="Select Microchip"
                  control={control}
                  errors={errors}
                  options={[
                    { label: "I Do Not Want", value: "I Do Not Want" },
                    {
                      label: "I Do Want ($67.03)",
                      value: "I Do Want ($67.03)",
                    },
                  ]}
                  clearable
                  required={false}
                />
                <DropdownSelect
                  name="earFlushing"
                  label="Ear Flushing"
                  placeholder="Select Ear Flushing"
                  control={control}
                  errors={errors}
                  options={[
                    { label: "I Do Not Want", value: "I Do Not Want" },
                    {
                      label: "I Do Want ($32.35)",
                      value: "I Do Want ($32.35)",
                    },
                  ]}
                  clearable
                  required={false}
                />
                <DropdownSelect
                  name="fecal"
                  label="Fecal"
                  placeholder="Select Fecal"
                  control={control}
                  errors={errors}
                  options={[
                    { label: "I Do Not Want", value: "I Do Not Want" },
                    {
                      label: "I Do Want ($21.13)",
                      value: "I Do Want ($21.13)",
                    },
                  ]}
                  clearable
                  required={false}
                />
              </div>

              <InfoSection
                title="Consent and Anesthesia Authorization"
                content="I understand that during the performance of the procedure(s), unseen conditions may be revealed that necessitate the foregoing of the procedure(s) recommended. Therefore, I consent and authorize the performance of such procedure(s) as are necessary in the exercise of the veterinarian's professional judgement. I also authorize the use of appropriate anesthesia or medications and understand the hospital support staff will be employed as deemed necessary by the veterinarian. If work up is declined, I fully understand and accept the possible consequences of anesthesia and surgery and or dentistry being performed without the knowledge obtained from the previously mentioned work up. Clicking I Agree below will act as the signature of the owner/agent and acknowledge obligation for payment in full for services rendered."
              />

              {/* Signature Consent */}
              <div>
                <label className="inline-flex items-center cursor-pointer">
                  <input
                    type="checkbox"
                    className="cursor-pointer"
                    {...register("agree", { required: true })}
                  />
                  <span className="ml-2">
                    I Agree (Signature of owner/agent)
                  </span>
                  <span className="text-red-500">*</span>
                </label>
                {errors.agree && (
                  <p className="text-red-500 text-sm">
                    You must agree before submitting.
                  </p>
                )}
              </div>

              {/* Submit */}
              {success && (
                <div className="mb-4 text-green-700 bg-green-100 border border-green-300 rounded p-2 text-center">
                  Surgical Consent Form submitted successfully.
                </div>
              )}
              <Button
                type="submit"
                disabled={loading}
                className="w-full border-2 border-[#0274D9] rounded-full bg-[#0274D9] text-white transition-all hover:bg-white hover:text-[#0274D9] py-3 text-md cursor-pointer"
              >
                {loading && (
                  <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin" />
                )}
                {loading ? "Submitting..." : "Submit"}
              </Button>
            </form>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

// export info section
const InfoSection = ({ title, content }: InfoSectionProps) => {
  return (
    <div className="border-b pb-4 mb-6">
      {title && (
        <h2 className="text-lg font-semibold text-gray-900">{title}</h2>
      )}
      {content && (
        <p className="mt-2 text-gray-700 text-sm leading-relaxed">{content}</p>
      )}
    </div>
  );
};

// Helper component for Yes/No options
const ConsentSection = ({
  label,
  field,
  register,
  errors,
  isRequired = true,
  options = [
    { label: "Do", value: "Do" },
    { label: "Do Not", value: "Do Not" },
  ],
}: ConsentSectionProps) => {
  return (
    <div>
      <p className="font-medium">
        {label} {isRequired && <span className="text-red-500">*</span>}{" "}
        {errors?.[field] && (
          <span className="inline-flex items-center rounded-md bg-red-50 px-2 py-1 text-xs font-medium text-red-700 ring-1 ring-red-600/10 ring-inset">
            <span className="text-red-500 text-[12px]">
              {errors[field]?.message?.toString() || "Required"}
            </span>
          </span>
        )}
      </p>
      <div className="flex space-x-4 mt-1">
        {options.map((option) => (
          <label
            key={option.value}
            className="flex items-center cursor-pointer"
          >
            <input
              type="radio"
              value={option.value}
              className="cursor-pointer"
              {...register(field, isRequired ? { required: "Required" } : {})}
            />
            <span className="ml-1">{option.label}</span>
          </label>
        ))}
      </div>
      {/* {errors?.[field] && <p className="text-red-500 text-sm">This field is required.</p>} */}
    </div>
  );
};

const DropdownSelect = ({
  name,
  label,
  placeholder = "Select...",
  options,
  control,
  errors,
  required = false,
  clearable = false,
}: DropdownSelectProps) => {
  return (
    <div className="space-y-2">
      {label && (
        <Label htmlFor={name}>
          {label}
          {required && <span className="text-red-500 ml-1">*</span>}
        </Label>
      )}

      <div className="relative">
        <Controller
          name={name}
          control={control}
          rules={{ required: required ? "This field is required." : false }}
          render={({ field: { onChange, value } }) => (
            <>
              <Select value={value} onValueChange={onChange}>
                <SelectTrigger className="pr-6">
                  <SelectValue placeholder={placeholder} />
                </SelectTrigger>
                <SelectContent>
                  {options.map((opt) => (
                    <SelectItem key={opt.value} value={opt.value}>
                      {opt.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>

              {clearable && value && (
                <button
                  type="button"
                  onClick={(e) => {
                    e.stopPropagation();
                    onChange("");
                  }}
                  className="absolute right-2 top-1/2 -translate-y-1/2 text-gray-400 hover:text-gray-600"
                >
                  <X className="w-4 h-4" />
                </button>
              )}
            </>
          )}
        />
        {errors?.[name] && (
          <p className="text-red-500 text-sm">
            {errors[name]?.message?.toString()}
          </p>
        )}
      </div>
    </div>
  );
};
export default SurgicalConsentForm;
