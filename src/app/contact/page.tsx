
'use client'
import { usePost<PERSON><PERSON> } from "@/services/useApi";
import { Mail, MapPin, Phone } from "lucide-react";
import { ChangeEvent, FormEvent, useState } from "react";

// Types
interface FormData {
  firstName: string;
  lastName: string;
  email: string;
  message: string;
}

interface Errors {
  firstName?: string;
  lastName?: string;
  email?: string;
  message?: string;
}


const ContactUs = () => {

    // api call to send contact info
  const { postData: postContactSendData, isLoading: postContactAPILoading, data:contactResponse } =
    usePostApi("");
  
    const [formData, setFormData] = useState<FormData>({
    firstName: "",
    lastName: "",
    email: "",
    message: "",
  });

  const [errors, setErrors] = useState<Errors>({});

   const handleChange = (e: ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { id, value } = e.target;

    setFormData((prevData) => ({
      ...prevData,
      [id.replace("-", "")]: value, // convert 'first-name' -> 'firstname'
    }));

    if (errors[id.replace("-", "") as keyof Errors]) {
      setErrors((prevErrors) => {
        const newErrors = { ...prevErrors };
        delete newErrors[id.replace("-", "") as keyof Errors];
        return newErrors;
      });
    }
  };

  const validate = (): Errors => {
    const newErrors: Errors = {};
    if (!formData.firstName.trim()) newErrors.firstName = "First name is required.";
    if (!formData.lastName.trim()) newErrors.lastName = "Last name is required.";
    if (!formData.email.trim()) newErrors.email = "Email is required.";
     else if (!/\S+@\S+\.\S+/.test(formData.email))
      newErrors.email = "Email is invalid";
    if (!formData.message.trim()) newErrors.message = "Message is required.";
    return newErrors;
  };

  const handleSubmit = async (e: FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    
    const validationErrors = validate();

    if (Object.keys(validationErrors).length > 0) {
      setErrors(validationErrors);
      return;
    }

    try {
      const response = await postContactSendData(formData, "https://53npngwlrgcbk5rhur3i3fddou0lkjui.lambda-url.us-west-2.on.aws/");

      if (response?.status===200) {
        setFormData({ firstName: "", lastName: "", email: "", message: "" });
        setErrors({});
      } else {
        console.error("Failed to send message. Try again later.");
      }
    } catch (error) {
      console.error("Error sending message:", error);
    }
  };

  return (
    <div>
      <div className="max-w-screen-xl mx-auto py-15 md:py-30 px-8 md:px-2">
        <div className="grid grid-cols-1 md:grid-cols-12 border border-primary shadow-xl rounded-[20px]">
          <div className="bg-secondary md:col-span-4 p-10 text-white rounded-[19px] md:rounded-l-[19px]">
            <p className="mt-4 text-sm leading-7 font-regular uppercase">
              Contact
            </p>
            <h3 className="text-3xl sm:text-4xl leading-normal font-extrabold tracking-tight">
              Get In <span className="">Touch</span>
            </h3>
            <p className="mt-4 leading-7 text-gray-200 mb-10">
              Drop us a line! We are here to answer your questions 24/7.
            </p>

            <div className="flex items-center mt-5">
              <MapPin size={24} className="mr-3 text-gray-300" />
              <span className="text-sm">
                38930 Blacow Rd, Ste B3, Fremont, CA 94536 USA
              </span>
            </div>
            <div className="flex items-center mt-5">
              <Phone size={22} className="mr-3 text-gray-300" />
              <a href="tel:****** 372 5811" className="flex items-center text-sm">
                {" "}
                <span>****** 372 5811</span>
              </a>
            </div>
            <div className="flex items-center mt-5">
              <Mail size={22} className="mr-3 text-gray-300" />
              <span className="text-sm"><EMAIL></span>
            </div>
          </div>
          <form className="md:col-span-8 p-10" onSubmit={handleSubmit} noValidate>
            <div className="flex flex-wrap -mx-3 mb-6">
              <div className="w-full md:w-1/2 px-3 mb-6 md:mb-0">
                <label
                  className="block uppercase tracking-wide text-gray-700 text-xs font-bold mb-2"
                  htmlFor="first-name"
                >
                  First Name <span className="text-red-500">*</span>
                </label>
                <input
                  id="firstName"
                  className="appearance-none bg-gray-100 border-b border-primary w-full text-gray-700 mr-3 p-4 leading-tight focus:outline-none focus:bg-gray-200"
                  type="text"
                  placeholder=""
                  aria-label="first name"
                   value={formData.firstName}
                onChange={handleChange}
                />
                {errors.firstName && <p className="text-red-500 text-xs mt-1">{errors.firstName}</p>}
              </div>
              <div className="w-full md:w-1/2 px-3">
                <label
                  className="block uppercase tracking-wide text-gray-700 text-xs font-bold mb-2"
                  htmlFor="last-name"
                >
                  Last Name <span className="text-red-500">*</span>
                </label>
                <input
                  id="lastName"
                  className="appearance-none bg-gray-100 border-b border-primary w-full text-gray-700 mr-3 p-4 leading-tight focus:outline-none focus:bg-gray-200"
                  type="text"
                  placeholder=""
                  aria-label="last name"
                  value={formData.lastName}
                  onChange={handleChange}
                />
                {errors.lastName && <p className="text-red-500 text-xs mt-1">{errors.lastName}</p>}
              </div>
            </div>
            <div className="flex flex-wrap -mx-3 mb-6">
              <div className="w-full px-3">
                <label
                  className="block uppercase tracking-wide text-gray-700 text-xs font-bold mb-2"
                  htmlFor="email"
                >
                  Email Address <span className="text-red-500">*</span>
                </label>
                <input
                  id="email"
                  className="appearance-none bg-gray-100 border-b border-primary w-full text-gray-700 mr-3 p-4 leading-tight focus:outline-none focus:bg-gray-200"
                  type="email"
                  placeholder=""
                  aria-label="email"
                  value={formData.email}
                  onChange={handleChange}
                />
                 {errors.email && <p className="text-red-500 text-xs mt-1">{errors.email}</p>}
              </div>
            </div>

            <div className="flex flex-wrap -mx-3 mb-6">
              <div className="w-full px-3">
                <label
                  className="block uppercase tracking-wide text-gray-700 text-xs font-bold mb-2"
                  htmlFor="message"
                >
                  Your Message <span className="text-red-500">*</span>
                </label>
                <textarea
                  rows={5}
                  id="message"
                  className="appearance-none bg-gray-100 border-b border-primary w-full text-gray-700 mr-3 p-4 focus:bg-gray-200 leading-tight focus:outline-none"
                  placeholder=""
                  aria-label="message"
                  value={formData.message}
                  onChange={handleChange}
                ></textarea>
                {errors.message && <p className="text-red-500 text-xs mt-1">{errors.message}</p>}
              </div>
              <div className={`flex ${contactResponse?'justify-between':'justify-end'}  w-full px-3 mt-3`}>
                {contactResponse && <div className="font-bold text-green-800">Your message has been received. Our team will contact you at the earliest.</div>}
                <button
                  // className="shadow bg-secondary focus:shadow-outline focus:outline-none text-white font-bold py-3 px-6 rounded"
                  className={`inline-block min-w-[200px] lg:px-6 py-2 px-3 cursor-pointer border-2 border-[#0274D9] rounded-full bg-[#0274D9] text-white transition-all hover:bg-white hover:text-[#0274D9] ${postContactAPILoading ? "pointer-events-none" : ""}`}
                  type="submit"
                >
                    {postContactAPILoading ? (
                  <div className="inline-block h-3 w-3 animate-spin rounded-full border-2 border-solid border-current border-r-transparent align-[-0.125em] motion-reduce:animate-[spin_1.5s_linear_infinite]"></div>
                ):' Send Message'}
                </button>
              </div>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

export default ContactUs;
