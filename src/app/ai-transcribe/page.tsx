import React from "react";
import {
  Stethoscope,
  Zap,
  Shield,
  Smartphone,
  Network,
  FileText,
} from "lucide-react";

export default function AITranscribeLanding() {
  const features = [
    {
      icon: <Stethoscope className="w-6 h-6 text-white" />,
      title: "Veterinary-Specific AI",
      description:
        "Trained on veterinary terminology, procedures, and medical language for unparalleled accuracy in pet healthcare documentation.",
      bgColor: "bg-blue-500",
    },
    {
      icon: <Zap className="w-6 h-6 text-white" />,
      title: "Real-Time Transcription",
      description:
        "Instant voice-to-text conversion while you examine patients. Never miss important details or observations again.",
      bgColor: "bg-purple-500",
    },
    {
      icon: <Shield className="w-6 h-6 text-white" />,
      title: "HIPAA Compliant",
      description:
        "Enterprise-grade security ensures all patient information remains confidential and meets veterinary privacy standards.",
      bgColor: "bg-indigo-500",
    },
    {
      icon: <Smartphone className="w-6 h-6 text-white" />,
      title: "Mobile-First Design",
      description:
        "Designed for busy veterinarians on the move. Works seamlessly on your smartphone or tablet, anywhere in your clinic.",
      bgColor: "bg-blue-500",
    },
    {
      icon: <Network className="w-6 h-6 text-white" />,
      title: "Smart Integration",
      description:
        "Automatically syncs with your existing veterinary practice management software and electronic health records.",
      bgColor: "bg-purple-500",
    },
    {
      icon: <FileText className="w-6 h-6 text-white" />,
      title: "Intelligent Formatting",
      description:
        "AI automatically organizes notes into structured formats: symptoms, diagnosis, treatment plans, and follow-up instructions.",
      bgColor: "bg-indigo-500",
    },
  ];

  return (
    <div className="min-h-screen bg-white">
      {/* Hero Section */}
      <div className="relative overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-br from-blue-600 via-purple-600 to-purple-800"></div>
        <div className="relative px-6 py-20 sm:px-8 lg:px-12">
          <div className="max-w-4xl mx-auto text-center">
            {/* Badge */}
            <div className="inline-flex items-center px-4 py-2 rounded-full bg-white/20 backdrop-blur-sm border border-white/30 mb-8">
              <span className="text-yellow-400 mr-2">⭐</span>
              <span className="text-white text-sm font-medium">
                AI-Powered Innovation
              </span>
            </div>

            {/* Heading */}
            <h1 className="text-4xl sm:text-5xl lg:text-6xl font-bold mb-6">
              <span className="text-yellow-400 tracking-wide mb-2">AI Transcribe</span>
              <br />
              <span className="text-white">for Veterinary</span>
              <br />
              <span className="text-white">Professionals</span>
            </h1>

            {/* Subheading */}
            <p className="text-lg sm:text-xl text-white/90 mb-8 max-w-2xl mx-auto leading-relaxed">
              Say goodbye to manual notes. Focus on care, we handle the rest.
              Revolutionary voice-to-text technology designed specifically for
              veterinary medicine.
            </p>

            {/* CTA Button */}
            <button className="inline-flex items-center px-8 py-3 rounded-full bg-white text-purple-600 font-semibold text-lg hover:bg-gray-50 transition-colors duration-200 shadow-lg">
              <span className="mr-2">🚀</span>
              Coming Soon
            </button>
          </div>
        </div>
      </div>

      {/* Features Section */}
      <div className="py-20 px-6 sm:px-8 lg:px-12">
        <div className="max-w-6xl mx-auto">
          {/* Section Header */}
          <div className="text-center mb-16">
            <h2 className="text-3xl sm:text-4xl  font-semibold text-gray-900 mb-6">
              The Future of Veterinary Documentation
            </h2>
            <p className="text-lg sm:text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
              Cutting-edge AI technology that understands veterinary terminology
              and converts your voice into accurate, professional notes
              instantly.
            </p>
          </div>

          {/* Features Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {features.map((feature, index) => (
              <div key={index} className="text-center group bg-blue-50 px-4 py-8 rounded-xl">
                {/* Icon */}
                <div
                  className={`inline-flex items-center justify-center w-16 h-16 rounded-2xl ${feature.bgColor} mb-6 group-hover:scale-110 transition-transform duration-200 shadow-lg`}
                >
                  {feature.icon}
                </div>

                {/* Content */}
                <h3 className="text-xl font-semibold text-gray-900 mb-4">
                  {feature.title}
                </h3>
                <p className="text-gray-700 leading-relaxed">
                  {feature.description}
                </p>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}
