"use client";

import { usePathname } from "next/navigation";
import Header from "./header";
import Footer from "./footer";

export default function LayoutWrapper({
  children,
}: {
  children: React.ReactNode;
}) {
  const pathname = usePathname();  
  const normalizedPath = pathname.replace(/\/$/, "");
  
  const isBarePage = ["/payment-success", "/payment-failed"].includes(normalizedPath);

  return (
    <>
      {!isBarePage && <Header />}
      <div className={!isBarePage ? "bg-white pt-16" : ""}>
        {children}
      </div>
      {!isBarePage && <Footer />}
    </>
  );
}
