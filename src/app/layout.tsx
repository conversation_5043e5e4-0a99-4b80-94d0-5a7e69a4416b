import CookieBanner from "@/components/cookieBanner";
import GoogleAnalytics from "@/components/googleAnalytics";
import type { Metadata } from "next";
import { Montserrat } from "next/font/google";
import "./globals.css";
import LayoutWrapper from "./LayoutWrapper";

const montserrat = Montserrat({
  subsets: ["latin"],
  variable: "--font-montserrat",
});

export const metadata: Metadata = {
  title: "MyVetHub",
  description:
    "Marketing, managing calls, appointments, and client engagement is tough. Let us handle the business side so you can focus on what you do best: caring for pets.",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body className={`${montserrat.variable}`}>
        <GoogleAnalytics />
        {/* new layout */}
        {/* <GoogleTagManagerNoScript /> */}
        <LayoutWrapper>{children}</LayoutWrapper>
        <CookieBanner />
        {/* old layout */}
        {/* <Header />
        <div className="bg-white pt-16">
        {children}
        </div>
        <Footer /> */}

        {/* <SubFooter /> */}
      </body>
    </html>
  );
}
