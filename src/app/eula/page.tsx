import PageBanner from "@/components/pageBanner";

export default function EULA() {
    return (
        <>
            <div className="about-banner">
                <PageBanner
                    title="End User License Agreement"
                    homeText=""
                    imageUrl="/images/Support-Services-min.png"
                />
            </div>

            <main className="container max-w-4xl mx-auto px-4 py-12 leading-relaxed">
                <p className="mb-8 leading-relaxed">
                    PLEASE READ THIS END USER LICENSE AGREEMENT (&quot;AGREEMENT&quot;) CAREFULLY
                    BEFORE USING THE PET VET APP (&quot;APPLICATION&quot;). BY INSTALLING,
                    ACCESSING, OR USING THIS APPLICATION, YOU AGREE TO BE BOUND BY THIS
                    AGREEMENT. IF YOU DO NOT AGREE, DO NOT USE THE APPLICATION.
                </p>

                <div className="space-y-10">
                    <Section title="1. License Grant">
                        <p>
                            PetVet Hub grants you a limited, non-exclusive, non-transferable,
                            revocable license to use the Application on any mobile device that
                            you own or control, in accordance with this Agreement, and subject
                            to the App Store or Google Play Store Terms of Service.
                        </p>
                    </Section>

                    <Section title="2. Platform Compliance">
                        <p className="font-semibold mb-6">For Apple iOS Users:</p>
                        <ul className="list-disc list-outside pl-6 space-y-2">
                            <li>This Agreement is between you and PetVet Hub, not Apple Inc.</li>
                            <li>
                                Apple is not responsible for any maintenance or support of the
                                Application.
                            </li>
                            <li>
                                In the event of any failure to conform to any warranty, you may
                                notify Apple for a refund (if applicable).
                            </li>
                            <li>
                                Apple is not responsible for addressing any claims relating to the
                                Application.
                            </li>
                        </ul>

                        <p className="font-semibold my-6">For Android (Google Play) Users:</p>
                        <ul className="list-disc list-outside pl-6 space-y-2">
                            <li>This Agreement is between you and PetVet Hub, not Google.</li>
                            <li>
                                Google is not responsible for the Application’s content or
                                performance.
                            </li>
                            <li>
                                Your use must comply with the Google Play Terms of Service.
                            </li>
                        </ul>
                    </Section>

                    <Section title="3. Restrictions">
                        <ul className="list-disc list-outside pl-6 space-y-2">
                            <li>No reverse engineering or source code extraction.</li>
                            <li>No illegal use or violation of third-party rights.</li>
                            <li>
                                No commercial exploitation (selling, leasing, sublicensing).
                            </li>
                            <li>No bypassing security features.</li>
                        </ul>
                    </Section>

                    <Section title="4. Data and Privacy">
                        <p>
                            By using the Application, you consent to our data practices as
                            outlined in our Privacy Policy. You are responsible for your login
                            credentials.
                        </p>
                    </Section>

                    <Section title="5. Updates and Modifications">
                        <p>
                            We may update the Application at any time. Continued use after an
                            update signifies your acceptance.
                        </p>
                    </Section>

                    <Section title="6. Termination">
                        <p>
                            We reserve the right to suspend or terminate your access at our
                            discretion, especially in cases of misuse.
                        </p>
                    </Section>

                    <Section title="7. Intellectual Property">
                        <p>
                            All rights remain with PetVet Hub. You are granted a license, not
                            ownership.
                        </p>
                    </Section>

                    <Section title="8. No Warranty">
                        <p>
                            The Application is provided “as is” without any warranties of any
                            kind.
                        </p>
                    </Section>

                    <Section title="9. Limitation of Liability">
                        <p>
                            PetVet Hub shall not be liable for damages arising from use or
                            inability to use the Application.
                        </p>
                    </Section>

                    <Section title="10. Governing Law">
                        <p>
                            This Agreement is governed by U.S. law. You agree to the
                            jurisdiction of its courts.
                        </p>
                    </Section>

                    <Section title="11. Third-Party Terms">
                        <p>
                            Use of third-party services through the Application is governed by
                            their respective terms.
                        </p>
                    </Section>

                    <Section title="12. Changes to This Agreement">
                        <p>
                            We may update this Agreement. Continued use after updates
                            constitutes acceptance.
                        </p>
                    </Section>

                    <p className="text-center text-red-600 mt-12">
                        BY CLICKING &quot;I AGREE&quot; OR USING THE APPLICATION, YOU ACKNOWLEDGE THAT
                        YOU HAVE READ AND AGREE TO THIS AGREEMENT.
                    </p>
                </div>
            </main>
        </>
    );
}

function Section({ title, children }: { title: string; children: React.ReactNode }) {
    return (
        <section>
            <h2 className="text-2xl md:text-3xl font-bold mb-4">{title}</h2>
            <div className="leading-relaxed">{children}</div>
        </section>
    );
}
