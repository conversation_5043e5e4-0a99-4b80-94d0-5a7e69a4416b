"use client";
import { ChevronDown } from "lucide-react";
import { useState } from "react";
import faqList from "@/constants/faq/faq.json";
import HoverButton from "@/components/common/hoverButton";

export default function FaqPage() {
  const [openIndex, setOpenIndex] = useState<number | null>(null);

  const toggleAccordion = (index: number) => {
    setOpenIndex(openIndex === index ? null : index);
  };

  return (
    <div className="py-15">
      <div className="container mx-auto px-4">
        <h2 className="text-3xl md:text-4xl font-medium text-center mb-3 text-primary">
          Questions You Might Be Asking…
        </h2>
        <div className="w-28 h-1 bg-gradient-to-l from-[#e4f2fe] to-[#fff3fe] mx-auto rounded-full mb-10"></div>

        <div className="max-w-4xl mx-auto grid gap-4">
          {faqList?.map((section, index) => (
            <div
              key={index}
              className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden transition-all duration-300"
            >
              <button
                onClick={() => toggleAccordion(index)}
                className="w-full px-6 py-5 text-left flex items-center justify-between bg-gray-50 transition-colors duration-200 cursor-pointer"
              >
                {/* bg-gradient-to-l from-[#e4f2fe] to-[#fff3fe] */}
                <span
                  className={`font-medium md:text-lg text-primary transition-colors duration-300 ${openIndex === index ? "text-[#0085D0]" : ""
                    }`}
                >
                  {section.question}
                </span>
                <ChevronDown
                  className={`w-5 h-5 text-primary transition-transform duration-300 ${openIndex === index ? "rotate-180" : ""
                    }`}
                />
              </button>

              <div
                className={`transition-all duration-300 ease-in-out px-6 overflow-hidden ${openIndex === index
                  ? "max-h-screen opacity-100 pb-6"
                  : "max-h-0 opacity-0 pb-0"
                  }`}
              >
                <p className="text-gray-700 text-lg leading-relaxed pt-2">
                  {section.answer}
                </p>
              </div>
            </div>
          ))}
        </div>

        {/* CTA Section */}
        <section className="bg-gradient-to-l from-[#e4f2fe] to-[#fff3fe] rounded-[20px] py-16 mt-10">
          <div className="text-center px-4 max-w-4xl mx-auto">
            <p className="text-xl sm:text-2xl font-medium text-gray-800 mb-4 leading-relaxed">
              Take <span className="text-primary font-semibold">MyVetHub</span> for a test drive and book a demo to find out what MyVetHub can do for you
            </p>

            <div className="mt-6 flex justify-center">
              <HoverButton
                text="Try MyVetHub Today"
                link="/contact"
                className=""
              />
            </div>
          </div>
        </section>
      </div>
    </div>
  );
}
