import { BLOG_LIST } from "../blog.constant";
interface Params {
  params: Promise<{
    slug: string;
  }>;
}

export async function generateMetadata({ params }: Params) {
  const { slug } = await params;
  const post = BLOG_LIST?.find(
    (post: { id: string }) => post.id === slug
  );

  if (!post) {
    return {
      title: "Post not found",
      description: "The requested post could not be found.",
    };
  }

  return {
    title: post?.title || "",
    description: post?.detail || "",
    openGraph: {
      images: [
        {
          url: post?.image,
          width: 1200,
          height: 630,
          alt: post?.title || "Vet Services",
        },
      ],
    },
  };
}

export default async function Page({
  params,
}: {
  params: Promise<{ slug: string }>;
}) {
  const { slug } = await params;
  const post = BLOG_LIST?.find((post) => post?.id === slug);

  if (!post) {
    return (
      <div>
        <h1>Post not found</h1>
        <p>{"The post you're looking for doesn't exist."}</p>
      </div>
    );
  }

  return (
    <div>
      {/* <ServicesBanner
        bannerImageUrl="/images/adorable-kitty-with-monochrome-wall-her1.jpeg"
        title={post?.heading || post?.title}
        // content={post?.content}
        alt={"Blogs"}
      /> */}
      <div className="container mx-auto px-4 py-15">
        {post?.content(post?.image, post?.title) ?? ""}
      </div>
    </div>
  );
}

export async function generateStaticParams() {
  return BLOG_LIST?.map((post) => ({
    slug: post.id,
  }));
}
