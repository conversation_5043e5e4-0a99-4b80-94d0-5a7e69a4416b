"use client";
import Image from "next/image";
import Link from "next/link";
import { BLOG_LIST } from "./blog.constant";

const AppointmentManager = () => {
  return (
    <div>
      {/* Header */}
      <section className="header">
        <div className="relative isolate py-24 sm:py-42 overflow-hidden">
          <div className="container mx-auto px-4 sm:px-8">
            <div className="absolute inset-0 -z-20 flex justify-end">
              <div className="relative w-1/2 h-full">
                <Image
                  src="/images/cute-little-dog.jpeg"
                  alt=""
                  className="absolute inset-0 h-full w-full object-cover object-center"
                  width={0}
                  height={0}
                  priority
                />
              </div>
            </div>
            <div className="absolute inset-0 -z-10 bg-gradient-to-r from-white via-yellow-50/100 to-transparent"></div>
            <div className="mx-auto lg:max-w-3xl lg:mx-0">
              <h2 className="text-3xl md:text-4xl font-medium">
                Our Blog&apos;s
              </h2>
            </div>
          </div>
        </div>
      </section>

      <section className="bg-white text-black py-5 md:py-14 px-4">
        <div className="container mx-auto">
          <div className="grid grid-cols-1 gap-4 md:grid-cols-2 xl:grid-cols-4">
            {BLOG_LIST?.map(({ id, title, detail, image }) => (
              <Link
                href={`/blog/${id}`}
                key={id}
                className="flex flex-col h-full shadow-xl pb-5 group overflow-hidden rounded-lg"
              >
                <div className="relative overflow-hidden rounded-lg">
                  <Image
                    className="object-cover object-center w-[100%] rounded-lg lg:h-52 transition-transform duration-500 ease-in-out group-hover:scale-105"
                    src={image}
                    alt={`${title}`}
                    width={500}
                    height={320}
                  />
                </div>

                {/* Make content area grow to equal height */}
                <div className="flex flex-col flex-grow px-4">
                  <h1 className="mt-6 font-medium text-lg line-clamp-2">
                    {title}
                  </h1>
                  <p className="flex-grow line-clamp-3 my-6">{detail}</p>{" "}
                  <Link href={`/blog/${id}`}>
                    <div className="group mt-0 inline-flex cursor-pointer items-center gap-x-1 font-medium text-primary">
                      Read More
                      <svg
                        className="shrink-0 size-4 transition ease-in-out group-hover:translate-x-1 group-focus:translate-x-1"
                        xmlns="http://www.w3.org/2000/svg"
                        width="24"
                        height="24"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                        strokeWidth="2"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                      >
                        <path d="m9 18 6-6-6-6" />
                      </svg>
                    </div>
                  </Link>
                </div>
              </Link>
            ))}
          </div>
        </div>
      </section>
    </div>
  );
};

export default AppointmentManager;

// "use client";
// import BLOG_DATA from "@/constants/blogs/blogs.json";
// import { ChevronLeft, ChevronRight } from "lucide-react";
// import Image from "next/image";
// import Link from "next/link";
// import { useEffect, useState } from "react";

// const BLOGS_PER_PAGE = 4;

// const AppointmentManager = () => {
//   const [currentPage, setCurrentPage] = useState(1);
//   const [fade, setFade] = useState(false);

//   const totalPages = Math.ceil(BLOG_DATA.length / BLOGS_PER_PAGE);

//   // const currentBlogs = BLOG_DATA.slice(
//   //   (currentPage - 1) * BLOGS_PER_PAGE,
//   //   currentPage * BLOGS_PER_PAGE
//   // );

//   const startIndex = (currentPage - 1) * BLOGS_PER_PAGE;
//   let currentSlice = BLOG_DATA.slice(startIndex, startIndex + BLOGS_PER_PAGE);

//   if (currentSlice.length < BLOGS_PER_PAGE && currentPage > 1) {
//     const needed = BLOGS_PER_PAGE - currentSlice.length;
//     const backfill = BLOG_DATA.slice(startIndex - needed, startIndex);
//     currentSlice = [...backfill, ...currentSlice];
//   }

//   const currentBlogs = currentSlice;

//   // Trigger fade animation
//   useEffect(() => {
//     setFade(false);
//     const timeout = setTimeout(() => setFade(true), 50);
//     return () => clearTimeout(timeout);
//   }, [currentPage]);

//   const handlePageChange = (page: number) => {
//     if (page >= 1 && page <= totalPages) {
//       setCurrentPage(page);
//     }
//   };

//   return (
//     <div>
//       {/* Header */}
//       <section className="header">
//         <div className="relative isolate py-24 sm:py-42 overflow-hidden">
//           <div className="container mx-auto px-4 sm:px-8">
//             <div className="absolute inset-0 -z-20 flex justify-end">
//               <div className="relative w-1/2 h-full">
//                 <Image
//                   src="/images/cute-little-dog.jpeg"
//                   alt=""
//                   className="absolute inset-0 h-full w-full object-cover object-center"
//                   width={0}
//                   height={0}
//                   priority
//                 />
//               </div>
//             </div>
//             <div className="absolute inset-0 -z-10 bg-gradient-to-r from-white via-yellow-50/100 to-transparent"></div>
//             <div className="mx-auto lg:max-w-3xl lg:mx-0">
//               <h2 className="text-3xl md:text-4xl font-medium">
//                 Our Blog&apos;s
//               </h2>
//             </div>
//           </div>
//         </div>
//       </section>

//       {/* Blog Grid */}
//       <section className="bg-white text-black py-10 md:py-20 px-4">
//         <div className="container mx-auto">
//           <div
//             className={`grid grid-cols-1 gap-4 mt-5 md:grid-cols-2 xl:grid-cols-4 transition-opacity duration-500 ${fade ? "opacity-100" : "opacity-0"
//               }`}
//           >
//             {currentBlogs.map(({ id, slug, title, subtitle, image }) => (
//               <div
//                 key={id}
//                 className="flex flex-col h-full shadow-xl pb-5 group overflow-hidden rounded-lg"
//               >
//                 <div className="relative overflow-hidden rounded-lg">
//                   <Image
//                     className="object-cover object-center w-full h-64 rounded-lg lg:h-80 transition-transform duration-500 ease-in-out group-hover:scale-105"
//                     src={image}
//                     alt={`blogImage_${id}`}
//                     width={500}
//                     height={320}
//                   />
//                 </div>
//                 <div className="flex flex-col flex-grow px-4">
//                   <h1 className="mt-6 font-medium text-lg md:h-20">{title}</h1>
//                   <p className="flex-grow line-clamp-3 my-6">{subtitle}</p>
//                   <Link href={`/blog/${slug}`}>
//                     <div className="group mt-5 inline-flex cursor-pointer items-center gap-x-1 font-medium text-primary">
//                       Read More
//                       <svg
//                         className="shrink-0 size-4 transition ease-in-out group-hover:translate-x-1"
//                         xmlns="http://www.w3.org/2000/svg"
//                         width="24"
//                         height="24"
//                         viewBox="0 0 24 24"
//                         fill="none"
//                         stroke="currentColor"
//                         strokeWidth="2"
//                         strokeLinecap="round"
//                         strokeLinejoin="round"
//                       >
//                         <path d="m9 18 6-6-6-6" />
//                       </svg>
//                     </div>
//                   </Link>
//                 </div>
//               </div>
//             ))}
//           </div>

//           {/* Pagination */}
//           <div className="flex justify-center md:justify-end mt-10 gap-4 items-center">
//             <button
//               onClick={() => handlePageChange(currentPage - 1)}
//               disabled={currentPage === 1}
//               className="p-2 bg-secondary text-white rounded-full disabled:opacity-30 cursor-pointer"
//               aria-label="Previous Page"
//             >
//               <ChevronLeft className="w-5 h-5" />
//             </button>

//             <span className="px-4 py-2 font-medium">
//               Page {currentPage} of {totalPages}
//             </span>

//             <button
//               onClick={() => handlePageChange(currentPage + 1)}
//               disabled={currentPage === totalPages}
//               className="p-2 bg-secondary text-white rounded-full disabled:opacity-30 cursor-pointer"
//               aria-label="Next Page"
//             >
//               <ChevronRight className="w-5 h-5" />
//             </button>
//           </div>

//         </div>
//       </section>
//     </div>
//   );
// };

// export default AppointmentManager;
