import React from "react";

const AboutUs = () => {
  return (
    <div className="pt-23">
      <div
        className="py-40 bg-cover bg-no-repeat bg-fixed"
        style={{
          backgroundImage: `url(https://img.freepik.com/free-vector/luxury-blue-golden-background_23-2149329427.jpg)`,
        }}
      >
        <div className="container m-auto text-center px-6 opacity-100">
          <h2 className="text-4xl font-bold mb-2 text-white">
            Echo Base...Ive got something!
          </h2>
          <h3 className="text-2xl mb-8 text-gray-200">
            Not much, but it could be a life form. This is Rouge Two. this is
            Rouge Two. Captain <PERSON>, so you copy?
          </h3>
          <button className="bg-secondary text-white font-bold rounded-full py-4 px-8 shadow-lg uppercase tracking-wider  transition-all">
            Commander Skywalker, do you copy?
          </button>
        </div>
      </div>
      <section className="pb-18 pt-20 dark:bg-dark">
        <div className="container mx-auto">
          <div className="-mx-4 mb-10 flex flex-wrap items-center lg:mb-[60px]">
            <div className="w-full px-4 lg:w-6/12">
              <div className="mb-[60px] max-w-[500px] xl:mb-[70px]">
                <span className="mb-2 block text-lg font-semibold text-primary">
                  About Our App
                </span>
                <h2 className="text-3xl font-bold text-dark dark:text-white sm:text-4xl md:text-[40px]">
                  All You Need to Know About The App
                </h2>
              </div>
              <div className="-mx-4 flex flex-wrap">
                <div className="w-full px-4 sm:w-1/2">
                  <div className="mb-10">
                    <div className="mb-6 flex h-[75px] w-[75px] items-center justify-center rounded-[20px] bg-[#4BA0FF] text-white">
                      <svg
                        width="35"
                        height="35"
                        viewBox="0 0 35 35"
                        className="fill-current"
                      >
                        <path d="M25.6484 10.8826V9.13259C25.6484 4.75759 22.3672 1.09353 18.2109 0.710713C15.9141 0.491963 13.6172 1.31228 11.9219 2.84353C10.2813 4.37478 9.29688 6.50759 9.29688 8.74978V10.8279C6.01563 11.3201 3.55469 14.1638 3.55469 17.5545V27.8904C3.55469 31.4451 6.45312 34.3435 10.0078 34.3435H24.9375C28.4922 34.3435 31.3906 31.4451 31.3906 27.8904V17.4998C31.4453 14.1638 28.9297 11.4295 25.6484 10.8826ZM13.2344 4.2654C14.5469 3.06228 16.2422 2.46071 18.0469 2.62478C21.2188 2.89821 23.7344 5.74196 23.7344 9.13259V10.7732H11.2656V8.74978C11.2656 7.05446 11.9766 5.41384 13.2344 4.2654ZM29.5313 27.8357C29.5313 30.3513 27.5078 32.3748 24.9922 32.3748H10.0078C7.49219 32.3748 5.46875 30.3513 5.46875 27.8357V17.6092C5.46875 14.9295 7.65625 12.6873 10.3906 12.6873H24.5547C27.2891 12.6873 29.5313 14.8201 29.5313 17.4998V27.8357Z" />
                        <path d="M17.5 19.4141C16.9531 19.4141 16.5156 19.8516 16.5156 20.3984V21.8203V23.2422V26.1406C16.5156 26.6875 16.9531 27.125 17.5 27.125C18.0469 27.125 18.4844 26.6875 18.4844 26.1406V23.2422V21.8203V20.3984C18.4844 19.8516 18.0469 19.4141 17.5 19.4141Z" />
                      </svg>
                    </div>
                    <h3 className="mb-3 text-xl font-semibold text-dark dark:text-white 2xl:text-[22px]">
                      Security Maintenance
                    </h3>
                    <p className="text-base leading-relaxed text-body-color dark:text-dark-6">
                      The little rotter bevvy I gormless mush golly gosh cras.
                    </p>
                  </div>
                </div>
                <div className="w-full px-4 sm:w-1/2">
                  <div className="mb-10">
                    <div className="mb-6 flex h-[75px] w-[75px] items-center justify-center rounded-[20px] bg-[#FBD06F] text-white">
                      <svg
                        width="35"
                        height="35"
                        viewBox="0 0 35 35"
                        className="fill-current"
                      >
                        <path d="M29.0391 21.1094V14.3828C29.0391 14.3281 29.0391 14.2734 29.0391 14.2187V7.38281C29.0391 3.60937 24.0078 0.65625 17.5547 0.65625C11.1016 0.65625 6.01562 3.66406 6.01562 7.38281V14.1641C6.01562 14.2187 6.01562 14.3281 6.01562 14.3828V21.1094C6.01562 21.1641 6.01562 21.2187 6.01562 21.2734V27.6172C6.01562 31.3359 11.1562 34.3437 17.5 34.3437C23.8438 34.3437 28.9844 31.3359 28.9844 27.6172V21.2734C28.9844 21.2187 29.0391 21.1641 29.0391 21.1094ZM8.36719 18.2109C10.5 19.7969 13.9453 20.7266 17.5 20.7266C21.1641 20.7266 24.5 19.7969 26.6328 18.2109C26.7969 18.1016 26.9609 17.9922 27.0703 17.8281V21.2187C26.9062 23.4062 23.0781 25.7031 17.5 25.7031C11.8672 25.7031 8.03906 23.4062 7.92969 21.1641V17.7734C8.03906 17.9922 8.20312 18.1016 8.36719 18.2109ZM7.92969 14.3281V11.1562C9.95312 12.9609 13.4531 14.1094 17.5 14.1094C21.5469 14.1094 25.0469 12.9609 27.0703 11.1562V14.2734C27.0156 15.3125 26.1953 16.1328 25.4844 16.625C23.6797 17.9922 20.7266 18.7578 17.5 18.7578C14.3281 18.7578 11.3203 17.9375 9.51562 16.625C8.80469 16.1875 7.98438 15.3672 7.92969 14.3281ZM17.5 2.625C22.6953 2.625 27.0703 4.8125 27.0703 7.4375C27.0703 10.0625 22.6953 12.25 17.5 12.25C12.3047 12.25 7.92969 10.0625 7.92969 7.4375C7.92969 4.8125 12.3047 2.625 17.5 2.625ZM17.5 32.4297C11.8672 32.4297 7.92969 29.9141 7.92969 27.6172V24.8281C9.95312 26.5781 13.4531 27.7266 17.5 27.7266C21.5469 27.7266 25.0469 26.5781 27.0703 24.8281V27.6172C27.0703 29.8594 23.1328 32.4297 17.5 32.4297Z" />
                      </svg>
                    </div>
                    <h3 className="mb-3 text-xl font-semibold text-dark dark:text-white 2xl:text-[22px]">
                      Backup Database
                    </h3>
                    <p className="text-base leading-relaxed text-body-color dark:text-dark-6">
                      The little rotter bevvy I gormless mush golly gosh cras.
                    </p>
                  </div>
                </div>
                <div className="w-full px-4 sm:w-1/2">
                  <div className="mb-10">
                    <div className="mb-6 flex h-[75px] w-[75px] items-center justify-center rounded-[20px] bg-[#A097FF] text-white">
                      <svg
                        width="35"
                        height="35"
                        viewBox="0 0 35 35"
                        className="fill-current"
                      >
                        <path d="M29.5312 21.656L28.6563 21.1638L29.6953 20.5623C30.7344 19.906 31.3359 18.8123 31.2812 17.6091C31.2266 16.406 30.625 15.3123 29.5312 14.7107L27.8906 13.781L29.6406 12.6873C30.6797 12.031 31.2812 10.9373 31.2266 9.73413C31.1719 8.53101 30.5703 7.43726 29.4766 6.83569L19.25 1.09351C18.2109 0.491943 16.9531 0.546631 15.9141 1.09351L5.41406 7.21851C4.375 7.82007 3.71875 8.91382 3.71875 10.1169C3.71875 11.3201 4.375 12.4138 5.41406 13.0154L7.10938 13.9998L5.41406 14.9841C4.375 15.5857 3.71875 16.6794 3.71875 17.8826C3.71875 19.0857 4.32031 20.1794 5.41406 20.781L6.39844 21.3279L5.46875 21.8748C4.42969 22.4763 3.77344 23.5701 3.77344 24.7732C3.77344 25.9763 4.375 27.0701 5.46875 27.6716L15.9141 33.6873C16.4609 34.0154 17.0078 34.1248 17.6094 34.1248C18.2109 34.1248 18.8125 33.9607 19.3594 33.6326L29.6953 27.2888C30.7344 26.6326 31.3359 25.5388 31.2812 24.3357C31.2266 23.2966 30.625 22.2029 29.5312 21.656ZM5.63281 10.1169C5.63281 9.57007 5.90625 9.13257 6.34375 8.85913L16.8438 2.78882C17.0625 2.67944 17.3359 2.57007 17.5547 2.57007C17.7734 2.57007 18.0469 2.62476 18.2656 2.73413L28.5469 8.47632C28.9844 8.74976 29.2578 9.18726 29.3125 9.73413C29.3125 10.281 29.0391 10.7185 28.6016 10.9919L18.3203 17.3904C17.8828 17.6638 17.2812 17.6638 16.8438 17.3904L6.39844 11.3748C5.90625 11.156 5.63281 10.6638 5.63281 10.1169ZM5.63281 17.9373C5.63281 17.3904 5.90625 16.9529 6.34375 16.6794L9.02344 15.1482L15.8594 19.0857C16.4062 19.4138 16.9531 19.5232 17.5547 19.5232C18.1562 19.5232 18.7578 19.3591 19.3047 19.031L26.0312 14.8748L28.6016 16.2966C29.0391 16.5701 29.3125 17.0076 29.3672 17.5544C29.3672 18.1013 29.0938 18.5388 28.6563 18.8123L18.3203 25.2654C17.8828 25.5388 17.2812 25.5388 16.8438 25.2654L6.39844 19.2498C5.90625 18.9763 5.63281 18.4841 5.63281 17.9373ZM28.6563 25.8123L18.3203 32.2107C17.8828 32.4841 17.2812 32.4841 16.8438 32.2107L6.39844 26.1951C5.96094 25.9216 5.6875 25.4841 5.6875 24.9373C5.6875 24.3904 5.96094 23.9529 6.39844 23.6794L8.3125 22.5857L15.8594 26.9607C16.4062 27.2888 16.9531 27.3982 17.5547 27.3982C18.1562 27.3982 18.7578 27.2341 19.3047 26.906L26.7969 22.2576L28.6563 23.2966C29.0938 23.5701 29.3672 24.0076 29.4219 24.5544C29.3672 25.0466 29.0938 25.5388 28.6563 25.8123Z" />
                      </svg>
                    </div>
                    <h3 className="mb-3 text-xl font-semibold text-dark dark:text-white 2xl:text-[22px]">
                      Server Maintenance
                    </h3>
                    <p className="text-base leading-relaxed text-body-color dark:text-dark-6">
                      The little rotter bevvy I gormless mush golly gosh cras.
                    </p>
                  </div>
                </div>
                <div className="w-full px-4 sm:w-1/2">
                  <div className="mb-10">
                    <div className="mb-6 flex h-[75px] w-[75px] items-center justify-center rounded-[20px] bg-[#48DB7A] text-white">
                      <svg
                        width="35"
                        height="35"
                        viewBox="0 0 35 35"
                        className="fill-current"
                      >
                        <path d="M30.2969 23.7344H26.3594C24.5547 23.7344 22.9687 24.9375 22.4766 26.6328H10.3359C8.09375 26.6328 6.23437 24.7734 6.23437 22.5312C6.23437 20.2891 8.09375 18.4297 10.3359 18.4297H24.6641C28 18.4297 30.6797 15.75 30.6797 12.4141C30.6797 9.07813 28 6.39844 24.6641 6.39844H12.6875V6.125C12.6875 3.88281 10.8828 2.07812 8.64062 2.07812H4.70312C2.46094 2.07812 0.65625 3.88281 0.65625 6.125V7.16406C0.65625 9.40625 2.46094 11.2109 4.70312 11.2109H8.64062C10.4453 11.2109 12.0312 10.0078 12.5234 8.3125H24.6641C26.9062 8.3125 28.7656 10.1719 28.7656 12.4141C28.7656 14.6563 26.9062 16.5156 24.6641 16.5156H10.3359C7 16.5156 4.32031 19.1953 4.32031 22.5312C4.32031 25.8672 7 28.5469 10.3359 28.5469H22.3125V28.8203C22.3125 31.0625 24.1172 32.8672 26.3594 32.8672H30.2969C32.5391 32.8672 34.3437 31.0625 34.3437 28.8203V27.7812C34.3437 25.5391 32.4844 23.7344 30.2969 23.7344ZM10.7734 7.21875C10.7734 8.36719 9.84375 9.35156 8.64062 9.35156H4.70312C3.55469 9.35156 2.57031 8.42188 2.57031 7.21875V6.17969C2.57031 5.03125 3.5 4.04688 4.70312 4.04688H8.64062C9.78906 4.04688 10.7734 4.97656 10.7734 6.17969V7.21875ZM32.4297 28.8203C32.4297 29.9688 31.5 30.9531 30.2969 30.9531H26.3594C25.2109 30.9531 24.2266 30.0234 24.2266 28.8203V27.7812C24.2266 26.6328 25.1562 25.6484 26.3594 25.6484H30.2969C31.4453 25.6484 32.4297 26.5781 32.4297 27.7812V28.8203Z" />
                      </svg>
                    </div>
                    <h3 className="mb-3 text-xl font-semibold text-dark dark:text-white 2xl:text-[22px]">
                      No Risk Protestable
                    </h3>
                    <p className="text-base leading-relaxed text-body-color dark:text-dark-6">
                      The little rotter bevvy I gormless mush golly gosh cras.
                    </p>
                  </div>
                </div>
              </div>
            </div>
            <div className="w-full px-4 lg:w-6/12">
              <div className="flex justify-center">
                {/* <img
                  src="https://demo.tailgrids.com/templates/app/build/src/assets/images/services/services-08/image-01.svg"
                  alt="image"
                  className="mx-auto max-w-full"
                /> */}
              </div>
            </div>
          </div>
        </div>
      </section>
      <section className="dark:bg-dark pb-18">
        <div className="container mx-auto">
          <div className="relative z-10 overflow-hidden rounded bg-primary dark:bg-dark-3">
            <div className="-mx-4 flex flex-wrap">
              <div className="w-full px-4 lg:w-1/2">
                <div className="px-6 py-14 sm:px-10 md:px-[70px] md:py-20 lg:pl-11 lg:pr-0">
                  <h2 className="mb-9 text-2xl font-bold leading-snug text-dark dark:text-white md:text-[40px]/[48px]">
                    Get support through our application.
                  </h2>
                  <p className="mb-11 text-lg text-body-color dark:text-dark-6">
                    Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed
                    do eiusmod tempor incididunt ut.
                  </p>
                  <div className="flex flex-wrap items-center">
                    <a
                      href="javascript:void(0)"
                      className="group my-1 mr-4 inline-flex h-[70px] items-center justify-center rounded-lg bg-secondary px-5 text-center text-base font-medium text-white shadow-[0px_5px_18px_0px_rgba(0,0,0,0.08)] transition duration-200 hover:text-primary dark:bg-dark-2 dark:text-white dark:hover:text-primary sm:px-6 md:text-lg"
                    >
                      <span className="flex h-11 w-11 items-center justify-center rounded-md bg-dark text-white transition duration-200 group-hover:text-primary dark:bg-white/10 dark:group-hover:text-primary">
                        <svg
                          width="24"
                          height="24"
                          viewBox="0 0 24 24"
                          fill="none"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            d="M20.8608 8.35849C20.7259 8.4615 18.3449 9.78251 18.3449 12.7199C18.3449 16.1175 21.3747 17.3194 21.4653 17.3492C21.4514 17.4225 20.984 18.9953 19.8679 20.5979C18.8727 22.0082 17.8333 23.4163 16.2522 23.4163C14.671 23.4163 14.2641 22.5119 12.4388 22.5119C10.66 22.5119 10.0275 23.446 8.58124 23.446C7.13494 23.446 6.12577 22.141 4.96549 20.5384C3.62153 18.6565 2.53564 15.7328 2.53564 12.958C2.53564 8.50733 5.47474 6.14685 8.3673 6.14685C9.90427 6.14685 11.1855 7.14049 12.1504 7.14049C13.0689 7.14049 14.5013 6.08734 16.2498 6.08734C16.9125 6.08734 19.2936 6.14685 20.8608 8.35849ZM15.4197 4.20311C16.1429 3.3583 16.6544 2.1861 16.6544 1.01389C16.6544 0.851341 16.6405 0.686502 16.6102 0.553711C15.4337 0.597211 14.0339 1.32526 13.1898 2.28912C12.5271 3.03091 11.9086 4.20311 11.9086 5.39134C11.9086 5.56995 11.9389 5.74849 11.9528 5.80571C12.0272 5.81948 12.1481 5.83549 12.2691 5.83549C13.3247 5.83549 14.6524 5.13949 15.4197 4.20311Z"
                            fill="currentColor"
                          />
                        </svg>
                      </span>
                      <span>App Store</span>
                    </a>
                    <a
                      href="javascript:void(0)"
                      className="group my-1 inline-flex h-[70px] items-center justify-center rounded-lg px-5 text-center text-base font-medium text-primary transition duration-200 bg-white hover:text-primary dark:text-dark-6 dark:hover:bg-dark-2 dark:hover:text-primary sm:px-6 md:text-lg"
                    >
                      <span className="mr-4">
                        <svg
                          width="24"
                          height="24"
                          viewBox="0 0 24 24"
                          fill="none"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            d="M3.85245 0.375L14.0977 10.6184L16.7415 7.9746C13.1289 5.78276 8.29042 2.84244 6.11441 1.52043L4.65656 0.637253C4.3989 0.480274 4.12297 0.395734 3.85245 0.375ZM2.28856 1.60143C2.26783 1.71398 2.25 1.82709 2.25 1.94853V22.1922C2.25 22.2771 2.26708 22.3562 2.27892 22.4372L12.7016 12.0145L2.28856 1.60143ZM18.4789 9.0294L15.4938 12.0145L18.4269 14.9475C19.9809 14.0056 21.0572 13.3515 21.1767 13.2795C21.7049 12.9566 22.0032 12.4695 21.9943 11.9373C21.9864 11.415 21.688 10.9481 21.1805 10.6608C21.067 10.5956 20.0053 9.95449 18.4789 9.0294ZM14.0977 13.4106L3.88331 23.625C4.08176 23.5905 4.27995 23.5312 4.46951 23.4167C4.73216 23.2568 11.8269 18.9523 16.6894 16.0023L14.0977 13.4106Z"
                            fill="currentColor"
                          />
                        </svg>
                      </span>
                      <span>Play Store</span>
                    </a>
                  </div>
                </div>
              </div>
              <div className="w-full self-end px-4 lg:w-1/2">
                <div className="hidden text-center lg:block">
                  {/* <img
                    src="https://demo.tailgrids.com/templates/app/build/src/assets/images/cta/mobile-01.svg"
                    alt="image"
                    className="mx-auto max-w-full"
                  /> */}
                </div>
              </div>
            </div>

            <div className="hidden md:block">
              <span className="absolute right-0 top-0 z-[-1]">
                <svg
                  width="329"
                  height="218"
                  viewBox="0 0 329 218"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <circle cx="182" cy="36" r="182" fill="#13C296" />
                </svg>
              </span>
              <span className="absolute right-0 top-20 z-[-1]">
                <svg
                  width="95"
                  height="161"
                  viewBox="0 0 95 161"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <circle cx="80.5" cy="80.5" r="80.5" fill="#3056D3" />
                </svg>
              </span>
            </div>
          </div>
        </div>
      </section>

    </div>
  );
};

export default AboutUs;
