// import { getDynamicConfig } from "@/constant/baseUrl";

// const { baseUrl } = getDynamicConfig();

export const metadata = {
  // metadataBase: new URL('https://veterinary.drptek.com'),
  title: "Digital Marketing For Veterinarians - MyVetHub",
  description:
    "",
  keywords:
    "",
  robots: "index, follow",
//   alternates: {
//     canonical: `${baseUrl}/about-veterinary-hospital/`,
//   },
//   openGraph: {
//     images: [
//       {
//         url: `${baseUrl}/images/portrait-cute-dog-playing-with-owner.avif`,
//         // url: "https://telemedicine-aws-prod.s3.us-west-2.amazonaws.com/Assets/adorable-kitty-with-monochrome-wall-her_23-2148955140.avif",
//         width: 1200,
//         height: 630,
//         alt: "my vet hub digital marketing",
//       },
//     ],
//     siteName: "",
//   },
};

export default function Layout({ children }: { children: React.ReactNode }) {
  return <div>{children}</div>;
}
