"use client";

import HoverButton from "@/components/common/hoverButton";
import featuresData from "@/constants/digitalMarketingForVeterinarians/digitalMarketingVeterinarians.json"
import digitalMarketingfaqList from "@/constants/digitalMarketingForVeterinarians/digitalMarketingfaqList.json";
import {
    BadgeCheck,
    CalendarDays,
    ChevronDown,
    MailCheck,
    ThumbsUp,
} from "lucide-react";
import Image from "next/image";
import { memo, useState } from "react";

const stats = [
    {
        icon: <CalendarDays className="mx-auto text-primary w-10 h-10 mb-4" />,
        value: "100%",
        label: "Booked appointments",
    },
    {
        icon: <ThumbsUp className="mx-auto text-primary w-10 h-10 mb-4" />,
        value: "94%",
        label: "More positive reviews",
    },
    {
        icon: <MailCheck className="mx-auto text-primary w-10 h-10 mb-4" />,
        value: "87%",
        label: "Boosted email activities",
    },
    {
        icon: <BadgeCheck className="mx-auto text-primary w-10 h-10 mb-4" />,
        value: "57%",
        label: "more appointments confirmations",
    },
];

const titleIconMap = {
    "Appointment Scheduling & Management": MailCheck,
    //   "Video Consultations & Telemedicine": Video,
    //   "AI-Powered VoIP Phone System": Phone,
    //   "Online Payments & Real-Time Reporting": CreditCard,
    //   "Onboarding, Training & Support": Users,
    //   "Custom Website Design & Development": MonitorSmartphone,
    //   "Digital Marketing Solutions": TrendingUp,
};

const parseBoldText = (text: string) => {
    const parts = text.split(/(\*\*.*?\*\*)/g); // split on **bold**
    return parts.map((part, idx) => {
        if (part.startsWith("**") && part.endsWith("**")) {
            return (
                <span key={idx} className="font-semibold text-lg">
                    {part.slice(2, -2)}
                </span>
            );
        }
        return <span key={idx}>{part}</span>;
    });
};

const MarketingSection = () => {
    const [openIndex, setOpenIndex] = useState<number | null>(null);

    const toggleAccordion = (index: number) => {
        setOpenIndex(openIndex === index ? null : index);
    };

    return (
        <>
            <section className="bg-primary md:py-20 py-14 px-4 md:px-16">
                <div className="container mx-auto flex flex-col md:flex-row items-center gap-12">
                    {/* Left Content */}
                    <div className="md:w-1/2 space-y-6">
                        <h1 className="mb-6 text-3xl md:text-5xl  text-primary flex items-center gap-3">
                            Digital Marketing For Veterinarians
                        </h1>
                        <p className="text-lg leading-relaxed text-gray-700">
                            MyVetHub is the complete marketing system built for veterinary hospitals. We help you attract new clients, keep your current ones coming back, and grow revenue without adding more to your plate. From local SEO to paid ads to client retention tools, every part of our strategy is built to bring real results.
                        </p>

                        <div className="flex gap-2 mt-6">
                            {[{ label: 'Schedule a Demo', link: '/contact' }, { label: 'Watch Video', link: '/contact' }].map((cta, i) => (
                                <HoverButton
                                    key={i}
                                    text={cta.label}
                                    link={cta.link}
                                    className=""
                                />
                            ))}
                        </div>
                    </div>

                    {/* Right Content - Image */}
                    <div className="md:w-1/2 flex justify-center">
                        <Image
                            src="/images/digital marketing.jpg"
                            alt="Vet marketing"
                            width={500}
                            height={500}
                            className="rounded-xl shadow-md object-contain"
                        />
                    </div>
                </div>
            </section>

            {/* benefit  start*/}
            <section className="bg-gradient-to-b from-[#EAF4FE] to-[#F6FAFE] py-16 px-6 md:px-12 lg:px-24">
                <div className="container mx-auto">
                    <h2 className="text-2xl md:text-4xl text-center mb-5">
                        Benefits of Digital Marketing For Veterinarians
                    </h2>
                    <p className="text-center mb-12 text-lg leading-relaxed text-gray-700">
                        Our team of veterinarian digital marketing experts understands the unique challenges and opportunities in the field and works closely with clinics to achieve their marketing goals.
                    </p>

                    <div className="grid grid-cols-1 md:grid-cols-2 lg:md:grid-cols-4 gap-8 text-center">
                        {stats?.map((item, idx) => (
                            <div
                                key={idx}
                                className="bg-[#E6F0FA] p-6 rounded-2xl shadow hover:shadow-lg transition duration-300 border-b-3 border-main"
                            >
                                {item?.icon}
                                <p className="text-2xl font-bold text-[#0073E6] mb-2">{item.value}</p>
                                <p className="text-gray-700 text-sm">{item.label}</p>
                            </div>
                        ))}
                    </div>
                </div>
            </section>
            {/* benefit  end*/}

            {/* feature product card start*/}
            <section className="px-4 py-5">
                <div className="container mx-auto space-y-5">
                    <h2 className="text-2xl md:text-4xl text-center mt-2">
                        Benefits of Digital Marketing For Veterinarians
                    </h2>
                    <p className="text-center text-lg leading-relaxed text-gray-700">
                        Digital marketing helps veterinary hospitals grow by connecting them with pet owners at the right time—online, on mobile, and locally. It doesn’t just attract clients—it keeps them engaged and loyal.
                    </p>
                    {featuresData?.map((item, index: number) => {
                        const Icon = (titleIconMap as Record<string, React.ElementType>)[item.title];
                        return <div
                            key={index}
                            className={`md:grid md:grid-cols-12 md:gap-x-12 py-5  items-center ${index % 2 === 1 ? "bg-gradient-to-r from-[#e0f2ff] via-[#f5faff] to-[#e0f2ff]" : "bg-white"
                                }`}
                        >
                            {/* Text */}
                            <div
                                className={`md:col-span-6 ${index % 2 === 1 ? "order-2 md:order-2 md:col-start-7" : "order-2 md:order-1"
                                    } text-black`}
                            >
                                <div className="xl:w-[90%]">
                                    <h2 className="mb-8 text-xl sm:text-2xl md:text-3xl text-primary flex items-center gap-3">
                                        {Icon && (
                                            <span className={`inline-flex items-center justify-center w-10 h-10 rounded-full shadow-md  
                                  ${index % 2 === 1 ? "bg-white text-blue-700" : "bg-blue-100 text-blue-700"
                                                }
                                  `}>
                                                <Icon className="w-5 h-5" />
                                            </span>
                                        )}
                                        {item.title}
                                    </h2>

                                    <div className="mb-6">
                                        <p>
                                            {parseBoldText(item.description)}
                                        </p>
                                    </div>

                                </div>
                            </div>

                            {/* Image */}
                            <div className={`md:col-span-6  ${index % 2 === 1 ? "order-1 md:order-1 md:col-start-1" : "order-1 md:order-2"
                                }`}>
                                <Image
                                    src={item.imageUrl}
                                    alt={item.title}
                                    width={600}
                                    height={400}
                                    className="rounded-lg w-full h-auto"
                                />
                            </div>
                        </div>
                    })}
                </div>
            </section>
            {/* feature product card end*/}

            {/* Discover how Myvethub start*/}
            <section className="bg-gradient-to-b from-[#EAF4FE] to-[#F6FAFE] py-10 px-4 text-center">
                <div className="max-w-6xl mx-auto">
                    <div className="bg-secondary border border-[#0274D9] rounded-xl p-8 shadow-md text-center">
                        <p className="mt-5 w-full md:w-[80%] mx-auto text-white">Discover how MyVetHub&apos;s Digital Marketing Services can enhance the online presence of your veterinary clinic starting now.</p>
                        <p className="mt-10">
                            <HoverButton
                                text="Book A Demo"
                                link="/contact"
                                className="bg-white text-primary border-white px-0 hover:bg-white hover:!text-black"
                            />
                        </p>
                    </div>
                </div>
            </section>
            {/* Discover how Myvethub end*/}

            {/* Questions Sections Start */}
            <section className="bg-primary pt-10 pb-15 px-4 sm:px-10 md:px-20">
                {/* bg-gradient-to-b from-[#EAF4FE] to-[#F6FAFE] */}
                <div className="max-w-5xl mx-auto">
                    <div className="text-center mb-10">
                        <h2 className="text-2xl md:text-4xl">
                            Learn More About Our Digital Marketing Services for Veterinary specialists
                        </h2>
                    </div>

                    <div className="space-y-6">
                        {digitalMarketingfaqList?.map((section, index) => (
                            <div
                                key={index}
                                className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden transition-all duration-300"
                            >
                                <button
                                    onClick={() => toggleAccordion(index)}
                                    className="w-full px-6 py-5 text-left flex items-center justify-between hover:bg-gray-50 transition-colors duration-200 cursor-pointer"
                                >
                                    <span
                                        className={`font-medium md:text-lg text-primary transition-colors duration-300 ${openIndex === index ? "text-[#0085D0]" : ""
                                            }`}
                                    >
                                        {section.question}
                                    </span>
                                    <ChevronDown
                                        className={`w-5 h-5 text-primary transition-transform duration-300 ${openIndex === index ? "rotate-180" : ""
                                            }`}
                                    />
                                </button>

                                <div
                                    className={`transition-all duration-300 ease-in-out px-6 overflow-hidden ${openIndex === index
                                        ? "max-h-screen opacity-100 pb-6"
                                        : "max-h-0 opacity-0 pb-0"
                                        }`}
                                >
                                    {section.answer?.map((item: string, index: number) =>
                                        <p key={index} className="text-gray-700 text-lg leading-relaxed pt-2">
                                            {item}
                                        </p>
                                    )}
                                </div>
                            </div>
                        ))}
                    </div>
                    {/* 
                    <div className="text-center mt-10">
                        <HoverButton
                            text="View More FAQs"
                            link="/faqs"
                            className=""
                        />
                    </div> */}
                </div>
            </section>
            {/* Questions Sections end */}

            <section className="bg-gradient-to-r from-[#e0f2ff] via-[#f5faff] to-[#e0f2ff] py-16">
                <div className="flex flex-col sm:flex-row items-center justify-between px-4 max-w-4xl mx-auto gap-4 text-center sm:text-left">
                    <p className="text-xl sm:text-2xl font-medium text-gray-800 leading-relaxed">
                        Want to learn more about how we can help your Practice?
                    </p>
                    <HoverButton text="Book a call" link="/contact" />
                </div>
            </section>
        </>
    );
};

export default memo(MarketingSection);
