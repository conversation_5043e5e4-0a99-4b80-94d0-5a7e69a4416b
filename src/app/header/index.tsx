"use client";

import HoverButton from "@/components/common/hoverButton";
import {
  productDropDownMenuItem,
  resourcesDropDownMenuItem,
} from "@/constants/dropdown-menu";
import { Menutype } from "@/models/common.model";
import { ChevronDown, ChevronRight } from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import { usePathname, useRouter } from "next/navigation";
import { useEffect, useRef, useState } from "react";

export default function Header() {
  // const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const [activeMenu, setActiveMenu] = useState<string | null>(null);
  const [isPageScroll, setPageScroll] = useState(false);
  const [activeDropdown, setActiveDropdown] = useState<
    "product" | "resources" | null
  >(null);

  const router = useRouter();
  const pathName = usePathname();
  const dropdownContainerRef = useRef<HTMLDivElement>(null);
  const isTouchDevice =
    typeof window !== "undefined" && "ontouchstart" in window;

  useEffect(() => {
    const handleScroll = () => {
      if (window.scrollY > 100) {
        setPageScroll(true);
      } else {
        setPageScroll(false);
      }
    };

    window.addEventListener("scroll", handleScroll);

    return () => {
      window.removeEventListener("scroll", handleScroll);
    };
  }, []);

  const toggleMobileMenu = () => {
    setIsMobileMenuOpen(!isMobileMenuOpen);
  };

  const handleMenuClick = (menu: string) => {
    setActiveMenu(activeMenu === menu ? null : menu);
  };

  // Helper function to determine if the link is active
  const isActive = (path: string) => pathName === path;

  const handleDropdownToggle = (menu: "product" | "resources") => {
    setActiveDropdown((prev) => (prev === menu ? null : menu));
  };

  const handleMouseEnter = (menu: "product" | "resources") => {
    if (!isTouchDevice) setActiveDropdown(menu);
  };

  const handleMouseLeave = (menu: "product" | "resources") => {
    if (!isTouchDevice && activeDropdown === menu) setActiveDropdown(null);
  };

  useEffect(() => {
    setActiveDropdown(null); // close dropdown on path change
  }, [pathName]);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        dropdownContainerRef.current &&
        !dropdownContainerRef.current.contains(event.target as Node)
      ) {
        setActiveDropdown(null);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  return (
    <header
      className={`${
        isPageScroll ? "shadow-md bg-primary" : "bg-white"
      } transform duration-300 ease-in-out w-full fixed z-50 sm:fixed`}
    >
      <div className="mx-auto flex justify-between items-center h-full">
        {/*mx-auto w-full max-w-7xl px-6 max-w-[80%] border rounded-full */}
        {/* mx-auto flex justify-between items-center h-full */}
        {/* Mobile Menu Toggle Button */}
        <div className="md:hidden flex items-center justify-between w-full pr-4 ps-4 py-1">
          <div className="p-0 sm:px-2">
            <Image
              src="/images/myVetHub_Logo.png"
              alt="Franchise Opportunity"
              loading="eager"
              className="cursor-pointer object-contain"
              onClick={() => router?.push("/")}
              width={100}
              height={100}
            />
          </div>

          <button
            onClick={toggleMobileMenu}
            className="flex items-center px-4 py-2 text-primary"
            aria-label={isMobileMenuOpen ? "Close menu" : "Open menu"}
          >
            {isMobileMenuOpen ? (
              <svg
                className="w-8 h-8"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M6 18L18 6M6 6l12 12"
                />
              </svg>
            ) : (
              <svg
                className="w-8 h-8"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M4 6h16M4 12h16m-7 6h7"
                />
              </svg>
            )}
          </button>
        </div>

        {/* Desktop Navigation */}

        <nav
          className={`container mx-auto hidden px-4 relative md:flex  w-full items-center justify-between`}
        >
          <div className="">
            <Image
              src="/images/myVetHub_Logo.png"
              alt="Franchise Opportunity"
              loading="eager"
              width={100}
              height={100}
              className="cursor-pointer"
              onClick={() => router?.push("/")}
            />
          </div>

          <div
            ref={dropdownContainerRef}
            className="flex items-center justify-evenly w-auto"
          >
            <Link
              href="/"
              className="text-black font-medium w-max py-9 px-2 lg:px-4 xl:px-6 text-md"
            >
              Home
            </Link>

            {/* Product Dropdown */}
            <div
              className="relative"
              onMouseEnter={() => handleMouseEnter("product")}
              onMouseLeave={() => handleMouseLeave("product")}
              onClick={() => handleDropdownToggle("product")}
            >
              <button className="text-black py-9 inline-flex items-center font-medium text-md cursor-pointer px-2 lg:px-4 xl:px-6">
                <span className="mr-1">Product</span>
                <ChevronDown
                  className={`w-5 h-5 transition-transform ${
                    activeDropdown === "product" ? "rotate-180" : ""
                  }`}
                />
              </button>

              {/* Mega Menu */}
              <div
                className={`fixed top-[95px] left-0 w-full bg-white shadow-lg z-50 transform origin-top transition-transform duration-300 ${
                  activeDropdown === "product"
                    ? "scale-y-100 opacity-100"
                    : "scale-y-0 opacity-0"
                }`}
              >
                <div className="mx-auto max-w-7xl px-6 py-6 grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
                  {productDropDownMenuItem.map((item, index) => (
                    <Link
                      key={index}
                      href={item.path}
                      onClick={() => setActiveDropdown(null)}
                      className={`flex items-start gap-4 p-3 hover:bg-gray-100 rounded transition  ${
                        isActive(`${item?.path}/`) ? "bg-[#E6F0FA]" : ""
                      }`}
                    >
                      <Image
                        src={item?.image || ""}
                        alt={item?.label}
                        width={44}
                        height={44}
                        className="w-[44px] h-[44px] object-contain"
                      />
                      <div>
                        <p className="text-base font-medium">{item?.label}</p>
                        <p className="text-sm text-gray-500">
                          {item?.description}
                        </p>
                      </div>
                    </Link>
                  ))}
                </div>
              </div>
            </div>

            {/* Resources Dropdown */}
            <div
              className="relative w-max px-2 lg:px-4 xl:px-6"
              ref={dropdownRef}
              onMouseEnter={() => handleMouseEnter("resources")}
              onMouseLeave={() => handleMouseLeave("resources")}
              onClick={() => handleDropdownToggle("resources")}
            >
              <button className="text-black py-9 inline-flex items-center font-medium text-md cursor-pointer">
                <span className="mr-1">Resources</span>
                <ChevronDown
                  className={`w-5 h-5 transition-transform ${
                    activeDropdown === "resources" ? "rotate-180" : ""
                  }`}
                />
              </button>

              <ul
                className={`absolute bg-white w-72 text-gray-700 mt-0 shadow-lg transition-all duration-500 origin-top transform ${
                  activeDropdown === "resources"
                    ? "scale-y-100 opacity-100"
                    : "scale-y-0 opacity-0"
                }`}
              >
                {resourcesDropDownMenuItem.map(
                  (item: Menutype, index: number) => (
                    <li key={index}>
                      <Link
                        href={item.path}
                        onClick={() => setActiveDropdown(null)}
                        className={`flex items-start gap-4 p-3 hover:bg-gray-100 rounded transition ${
                          isActive(`${item?.path}/`) ? "bg-[#E6F0FA]" : ""
                        }`}
                      >
                        <Image
                          src={item?.image || ""}
                          alt={item?.label}
                          width={36}
                          height={36}
                          className="w-[36px] h-[36px] object-contain"
                        />
                        <div>
                          <p className="text-base font-medium">{item?.label}</p>
                          <p className="text-sm text-gray-500">
                            {item?.description}
                          </p>
                        </div>
                      </Link>
                    </li>
                  )
                )}
              </ul>
            </div>

            <Link
              href="/contact"
              className="text-black py-9 font-medium w-max px-2 lg:px-4 xl:px-6 text-md mr-5"
            >
              Contact
            </Link>

            <HoverButton text="Book a Demo" link="/contact" className="" />
            {/* bg-black text-white border-black px-0 hover:bg-white hover:text-black */}
          </div>
        </nav>
      </div>

      {/* Mobile Dropdown Menu */}
      <div
        className={`md:hidden ${
          isMobileMenuOpen ? "max-h-[calc(100vh-90px)]" : "max-h-0"
        } overflow-y-auto transition-all duration-300 ease-in-out bg-primary shadow-xl`}
      >
        <nav className="flex flex-col space-y-2 pt-4 pb-6">
          <Link
            href="/"
            className={`text-black  mb-2 py-2 px-2 rounded-sm ${
              isActive("/") ? "bg-[#b8d7f3]" : ""
            }`}
            onClick={() => setIsMobileMenuOpen(false)}
          >
            Home
          </Link>
          <div className="relative" ref={dropdownRef}>
            <button
              onClick={(e) => {
                e.stopPropagation();
                handleMenuClick("product");
              }}
              className={`text-black py-2 flex items-center w-full px-2 ${
                isActive("/product/") || pathName?.includes("/product/")
                  ? "bg-[#b8d7f3]"
                  : ""
              }`}
            >
              Product
              {/* <ChevronDown
                size={16}
                onClick={(e) => {
                  e.stopPropagation();
                  handleMenuClick("services");
                }}
              /> */}
              {activeMenu === "product" ? (
                <ChevronDown
                  size={16}
                  className="ml-2"
                  onClick={(e) => {
                    e.stopPropagation();
                    handleMenuClick("product");
                  }}
                />
              ) : (
                <ChevronRight
                  size={16}
                  className="ml-2"
                  onClick={(e) => {
                    e.stopPropagation();
                    handleMenuClick("product");
                  }}
                />
              )}
            </button>
            {activeMenu === "product" && (
              <ul className="flex flex-col space-y-3 py-2 bg-primary text-sm">
                {productDropDownMenuItem?.map(
                  (item: Menutype, index: number) => (
                    <li key={index} className="flex-grow">
                      <Link
                        key={index}
                        href={item.path}
                        onClick={() => setIsMobileMenuOpen(false)}
                        className={`flex items-start gap-4 p-3 hover:bg-gray-100 rounded transition  ${
                          isActive(`${item?.path}/`) ? "bg-[#E6F0FA]" : ""
                        }
                            `}
                      >
                        <div className="w-[28px] h-[28px] flex-shrink-0">
                          <Image
                            src={item?.image || ""}
                            alt={item?.label}
                            width={44}
                            height={44}
                            className="w-full h-full object-contain"
                          />
                        </div>
                        <div className="flex-1">
                          <p className="font-medium">{item?.label}</p>
                          <p className="text-gray-500">{item?.description}</p>
                        </div>
                      </Link>
                    </li>
                  )
                )}
              </ul>
            )}
          </div>
          <div className="relative" ref={dropdownRef}>
            <button
              onClick={(e) => {
                e.stopPropagation();
                handleMenuClick("resources");
              }}
              className={`text-black py-2 flex items-center w-full px-2 ${
                ["/blog/", "/faqs/"]?.includes(pathName) ? "bg-[#b8d7f3]" : ""
              }`}
            >
              Resources
              {/* <ChevronDown
                  size={16}
                  onClick={(e) => {
                    e.stopPropagation();
                    handleMenuClick("about");
                  }}
                /> */}
              {activeMenu === "resources" ? (
                <ChevronDown
                  className="ml-2"
                  size={16}
                  onClick={(e) => {
                    e.stopPropagation();
                    handleMenuClick("resources");
                  }}
                />
              ) : (
                <ChevronRight
                  size={16}
                  className="ml-2"
                  onClick={(e) => {
                    e.stopPropagation();
                    handleMenuClick("resources");
                  }}
                />
              )}
            </button>
            {activeMenu === "resources" && (
              <ul className="flex flex-col space-y-3 py-2 bg-primary text-sm">
                {resourcesDropDownMenuItem?.map(
                  (item: Menutype, index: number) => (
                    <li key={index} className="flex-grow">
                      <Link
                        key={index}
                        href={item.path}
                        onClick={() => setIsMobileMenuOpen(false)}
                        className={`flex items-start gap-4 p-3 hover:bg-gray-100 rounded transition  ${
                          isActive(`${item?.path}/`) ? "bg-[#E6F0FA]" : ""
                        }
                            `}
                      >
                        <div className="w-[28px] h-[28px] flex-shrink-0">
                          <Image
                            src={item?.image || ""}
                            alt={item?.label}
                            width={36}
                            height={36}
                            className="w-full h-full object-contain"
                          />
                        </div>
                        <div className="flex-1">
                          <p className="font-medium">{item?.label}</p>
                          <p className="text-gray-500">{item?.description}</p>
                        </div>
                      </Link>
                    </li>
                  )
                )}
              </ul>
            )}
          </div>
          <Link
            href="/contact"
            className={`text-black mb-2 py-2 px-2 rounded-sm ${
              isActive("/contact/") ? "bg-[#b8d7f3]" : ""
            }`}
            onClick={() => setIsMobileMenuOpen(false)}
          >
            Contact Us
          </Link>
          <HoverButton text="Book a Demo" link="/contact" className="" />
          {/* bg-black mx-2 mt-2 text-white border-black px-0 hover:bg-white hover:text-black */}

          {/* <Button
            label="Book Appointment"
            className="bg-white text-primary font-semibold hover:bg-[#FFD014] hover:text-black py-2 px-4 rounded-md"
            onClick={() =>
              initCalendlyPopup("https://calendly.com/abhijeetashinde/30min")
            }
          /> */}
          {/* <Button
            label="Staff Login"
            className="bg-white text-primary font-semibold hover:bg-[#FFD014] hover:text-black py-2 px-4 rounded-md"
            onClick={() => router?.push("/admin/login")}
          /> */}
        </nav>
      </div>
    </header>
  );
}
