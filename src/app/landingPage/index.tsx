"use client";
import ChallengeSolutionSection from "@/components/challengeSolution";
import HoverButton from "@/components/common/hoverButton";
import TestimonialsSection from "@/components/testimonial";
import BLOG_DATA from "@/constants/blogs/blogs.json";
import faqList from "@/constants/faq/faq.json";
import featuresData from "@/constants/productFeatures/productFeatures.json";
import { CalendarClock, ChevronDown, CreditCard, HandCoins, MonitorSmartphone, Phone, PiggyBank, ShieldCheck, Star, TrendingUp, Users, Video } from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import { useState } from "react";

// const stats = [
//   {
//     icon: <Users className="mx-auto text-primary w-10 h-10 mb-4" />,
//     value: '2000+',
//     label: 'Veterinary Hospitals Trust MyVetHub ',
//   },
//   {
//     icon: <Star className="mx-auto text-primary w-10 h-10 mb-4" />,
//     value: '4.9★',
//     label: 'Rated on Capterra and Google Business',
//   },
//   {
//     icon: <ShieldCheck className="mx-auto text-primary w-10 h-10 mb-4" />,
//     value: '98%',
//     label: 'Customer Satisfaction Score',
//   },
//   {
//     icon: <CalendarCheck className="mx-auto text-primary w-10 h-10 mb-4" />,
//     value: 'All-In-One',
//     label: 'Hub for All Solutions',
//   },
// ];

// const featuresList: React.ReactNode[] = [
//   'Trusted by 2,000+ Veterinary Hospitals',
//   '98% Customer Satisfaction Score',
//   <>
//     Top-rated{' '}
//     <span className="inline-flex items-center gap-0.5 text-yellow-400">
//       {[...Array(5)].map((_, i) => (
//         <Star key={i} className="w-4 h-4 fill-yellow-400 stroke-yellow-400" />
//       ))}
//     </span>{' '}
//     on Capterra & Google
//   </>,
//   'One Hub for Scheduling, Payments, and More',
// ];

const stats = [
  {
    icon: <HandCoins className="mx-auto text-yellow-500 w-10 h-10 mb-4" />,
    value: '$2,500',
    label: 'Additional Revenue Per Week',
  },
  {
    icon: <PiggyBank className="mx-auto text-yellow-500 w-10 h-10 mb-4" />,
    value: '20%',
    label: 'Savings in Marketing Spend',
  },
  {
    icon: <TrendingUp className="mx-auto text-yellow-500 w-10 h-10 mb-4" />,
    value: '100%',
    label: 'Increase in Business Reach',
  },
  {
    icon: <Star className="mx-auto text-yellow-500 w-10 h-10 mb-4" />,
    value: '4.9★',
    label: 'Rated on Capterra and Google Business',
  },
  {
    icon: <ShieldCheck className="mx-auto text-yellow-500 w-10 h-10 mb-4" />,
    value: '100%',
    label: 'Controlled Drug Compliance',
  },
];


// const features = [
//   {
//     text: 'Appointments booking with a smart scheduler.',
//     icon: CalendarCheck,
//   },
//   {
//     text: 'Send automated confirmations and reminders.',
//     icon: Bell,
//   },
//   {
//     text: 'Manage payments, records, and analytics in one dashboard.',
//     icon: CreditCard,
//   },
//   {
//     text: 'Conduct video consultations.',
//     icon: Video,
//   },
// ];

const titleIconMap = {
  "Appointment Scheduling & Management": CalendarClock,
  "Video Consultations & Telemedicine": Video,
  "AI-Powered VoIP Phone System": Phone,
  "Online Payments & Real-Time Reporting": CreditCard,
  "Onboarding, Training & Support": Users,
  "Custom Website Design & Development": MonitorSmartphone,
  "Digital Marketing Solutions": TrendingUp,
};


const LandingPage = () => {
  const limitedBlogs = BLOG_DATA?.slice(0, 4);

  const [openIndex, setOpenIndex] = useState<number | null>(null);

  const toggleAccordion = (index: number) => {
    setOpenIndex(openIndex === index ? null : index);
  };

  return (
    <div>
      <section className="header py-5 md:py-20 pt-16 text-center lg:text-left bg-gradient-to-b">
        <div className="container mx-auto px-4 grid lg:grid-cols-2 lg:gap-x-8 gap-y-15 items-center">
          <div className="">
            <h1 className="text-3xl md:text-5xl mb-5">
              <p className="mb-2 text-black">MyVetHub</p>
              <p className="text-primary -ml-1">
                Turn Engagement Into Revenue
              </p>
            </h1>


            <p className="mb-8 text-md md:text-lg lg:mr-5 text-black">
              <b> Drive more revenue and stronger client relationships with a single platform designed for veterinary growth.</b> From online bookings and video consults to digital payments, reminders, and client communication—MyVetHub helps you stay connected and top-of-mind with pet owners.<b> Less chasing. More loyalty.</b>
            </p>

            {/* <HoverButton
              text="Get Started"
              link="/"
              className="py-3 mr-3 bg-black text-white border-black hover:bg-white hover:text-black"
            /> */}
            <div className="flex gap-2 flex-wrap justify-center md:justify-start">
              <HoverButton
                text="Book a Demo"
                link="/contact"
                className=""
              />
              <HoverButton
                text="Explore Products"
                link="/contact"
                className="bg-yellow-500 border-yellow-500 !text-black"
              />
              {/* <HoverButton
                text="Watch How It Works"
                link="/contact"
                className="py-3"
              /> */}
            </div>

          </div>
          {/* <div>
            <Image
              className="m-auto rounded-xl shadow-lg"
              src="/images/bannerImage.png"
              alt="alternative"
              style={{ width: "100%", height: "auto" }}
              width={0}
              height={0}
            />
          </div> */}
          <div className={`relative h-[300px] sm:h-[350px] md:h-[600px] w-full
                }`}>
            {/* max-w-[700px] */}
            <div className="absolute inset-5 rounded-lg overflow-hidden">
              <Image
                src='/images/home_01.png'
                alt='alternative'
                // width={600}
                // height={400}
                fill
                className="rounded-lg object-cover object-bottom"
              />
            </div>
          </div>
        </div>
      </section>

      {/* Why Choose */}
      <section className="container mx-auto rounded-[20px] bg-gradient-to-l from-[#e4f2fe] to-[#fff3fe] py-10 md:py-16 px-6 md:px-12 lg:px-24">
        {/* bg-gradient-to-b from-[#EAF4FE] to-[#F6FAFE] */}
        <div className="">
          <h2 className="text-2xl md:text-4xl text-center mb-12">
            Why Veterinary Hospitals Choose <span className="text-primary italic">MyVetHub</span>
          </h2>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:md:grid-cols-5 gap-8 text-center">
            {stats?.map((item, idx) => (
              <div
                key={idx}
                className="bg-[#E6F0FA] p-6 rounded-2xl shadow hover:shadow-md transition duration-300 border-t-2 border-main"
              >
                {item?.icon}
                <p className="text-2xl font-bold text-primary mb-2">{item.value}</p>
                <p className="text-gray-700 text-sm">{item.label}</p>
                {/* {featuresList[idx] && (
                  <p className="text-gray-700 text-sm flex items-start gap-2 mt-4 text-left justify-center">
                    <CheckCircle className="text-primary w-5 h-5 shrink-0" />
                    <span className="leading-snug">{featuresList[idx]}</span>
                  </p>
                )} */}
              </div>
            ))}
          </div>
          {/* 
          <div className="text-center mt-10">
            <HoverButton
              text="Book a Free Demo"
              link="/contact"
              className="bg-black text-white border-black px-0 hover:bg-white hover:text-black"
            />
          </div> */}
        </div>
      </section>

      {/*Accelerate Section  */}
      <section className="py-5 md:py-10 px-4 sm:px-8 lg:px-20">
        <div className="max-w-7xl mx-auto">
          <div className="p-8 text-center">
            <h2 className="text-2xl md:text-4xl mb-4 text-primary">
              Accelerate Your Hospital&apos;s Success with MyVetHub
            </h2>

            <div className="text-center mt-10">
              <HoverButton
                text="Book a Demo"
                link="/contact"
                className=""
              />
            </div>
          </div>
        </div>
      </section>


      <ChallengeSolutionSection />

      {/*  We Solve the Everyday */}
      {/* <section className="px-4">
        <div className="max-w-2xl w-full px-4 mx-auto mb-12 text-center lg:mb-10">
          <h2 className="mb-3 text-xl sm:text-2xl md:text-3xl text-black">
            We Solve the Everyday{" "}
            <span className="text-primary">Headaches</span> of Running a{" "}
            <span className="italic font-medium">Veterinary Hospital</span>
          </h2>
        </div>
        <div className="container  mx-auto">
          <div className="grid sm:grid-cols-2 lg:grid-cols-4 gap-x-4 gap-y-4">
            <div className="group hover:shadow-md shadow-xl border border-gray-100 bg-linear-to-b hover:from-[#f7f7f7] hover:to-[#f7f7f7] rounded-md transition-all duration-300  px-5 py-3 grid">
              <div className="inline-flex justify-left items-center  border-transparent">
                <Image
                  src="/images/arrows.png"
                  alt="arrows image"
                  width={60}
                  height={60}
                />
              </div>
              <div className="bg-linear-to-r from-gray-100 via-gray-50 to-transparent h-0.5 mt-6  ">
                <div className="bg-black w-9 h-0.5 "></div>
              </div>
              <div className="mt-5">
                <h3 className="font-medium text-lg md:text-xl pb-3 h-14 text-black">
                  Too Many Calls, Too Little Time?
                </h3>
                <p className="mt-1 text-black">
                  Our voice-enabled smartphones keep your team connected without
                  the chaos{" "}
                </p>
              </div>
              <div className="mt-3 inline-flex cursor-pointer items-center gap-x-1 font-medium text-primary ">
                Learn more
                <svg
                  className="shrink-0 size-4 transition ease-in-out group-hover:translate-x-1 group-focus:translate-x-1"
                  xmlns="http://www.w3.org/2000/svg"
                  width="24"
                  height="24"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                >
                  <path d="m9 18 6-6-6-6" />
                </svg>
              </div>
            </div>

            <div className="group hover:shadow-md shadow-xl border border-gray-100 bg-linear-to-b hover:from-[#f7f7f7] hover:to-[#f7f7f7] rounded-md transition-all duration-300  px-5 py-3 grid">
              <div className="inline-flex justify-left items-center  border-transparent">
                <Image
                  src="/images/cancel.png"
                  alt="cancel image"
                  width={60}
                  height={60}
                />
              </div>
              <div className="bg-linear-to-r from-gray-100 via-gray-50 to-transparent h-0.5 mt-6  ">
                <div className="bg-black w-9 h-0.5 "></div>
              </div>
              <div className="mt-5">
                <h3 className="font-medium text-lg md:text-xl pb-3 h-14 text-black">
                  Missed Appointments?
                </h3>
                <p className="mt-1 text-black">
                  Smart reminders and easy scheduling mean fewer no-shows
                  <p className="h-6"></p>
                </p>
              </div>
              <div className="mt-3 inline-flex cursor-pointer items-center gap-x-1 font-medium text-primary ">
                Learn more
                <svg
                  className="shrink-0 size-4 transition ease-in-out group-hover:translate-x-1 group-focus:translate-x-1"
                  xmlns="http://www.w3.org/2000/svg"
                  width="24"
                  height="24"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                >
                  <path d="m9 18 6-6-6-6" />
                </svg>
              </div>
            </div>

            <div className="group hover:shadow-md shadow-xl border border-gray-100 bg-linear-to-b hover:from-[#f7f7f7] hover:to-[#f7f7f7] rounded-md transition-all duration-300 px-5 py-3 grid">
              <div className="inline-flex justify-left items-center  border-transparent">
                <Image
                  src="/images/need.png"
                  alt="need image"
                  width={60}
                  height={60}
                />
              </div>
              <div className="bg-linear-to-r from-gray-100 via-gray-50 to-transparent h-0.5 mt-6  ">
                <div className="bg-black w-9 h-0.5 "></div>
              </div>
              <div className="mt-5">
                <h3 className=" font-medium text-lg md:text-xl pb-3 h-14 text-black">
                  Losing Clients?
                </h3>
                <p className="mt-1 text-black ">
                  Personalized pet health reminders keep pet owners engaged and
                  coming back{" "}
                </p>
              </div>
              <div className="mt-3 inline-flex cursor-pointer items-center gap-x-1 font-medium text-primary ">
                Learn more
                <svg
                  className="shrink-0 size-4 transition ease-in-out group-hover:translate-x-1 group-focus:translate-x-1"
                  xmlns="http://www.w3.org/2000/svg"
                  width="24"
                  height="24"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                >
                  <path d="m9 18 6-6-6-6" />
                </svg>
              </div>
            </div>

            <div className="group hover:shadow-md shadow-xl border border-gray-100 bg-linear-to-b hover:from-[#f7f7f7] hover:to-[#f7f7f7] rounded-md transition-all duration-300 px-5 py-3 grid">
              <div className="inline-flex justify-left items-center border-transparent">
                <Image
                  src="/images/marketing.png"
                  alt="marketing image"
                  width={60}
                  height={60}
                />
              </div>
              <div className="bg-linear-to-r from-gray-100 via-gray-50 to-transparent h-0.5 mt-6  ">
                <div className="bg-black w-9 h-0.5 "></div>
              </div>
              <div className="mt-5">
                <h3 className="font-medium text-lg md:text-xl pb-3 h-14 text-black">
                  Not Getting Noticed Online?{" "}
                </h3>
                <p className="mt-1  text-black">
                  We help you boost brand awareness and bring in new clients
                  with smart digital marketing{" "}
                </p>
              </div>
              <div className="mt-3 inline-flex cursor-pointer items-center gap-x-1 font-medium text-primary ">
                Learn more
                <svg
                  className="shrink-0 size-4 transition ease-in-out group-hover:translate-x-1 group-focus:translate-x-1"
                  xmlns="http://www.w3.org/2000/svg"
                  width="24"
                  height="24"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                >
                  <path d="m9 18 6-6-6-6" />
                </svg>
              </div>
            </div>
          </div>
        </div>
      </section> */}

      {/* Build For Veterinary */}
      {/* <section className="py-5 px-4 sm:px-8 lg:px-20">
        <div className="max-w-7xl mx-auto">
       
          <div className="text-center mb-10">
            <h2 className="text-2xl md:text-4xl">Built For Veterinary Hospitals</h2>
            <p className="text-xl py-2">Everything is in sync</p>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {features.map(({ text, icon: Icon }, idx) => (
              <div
                key={idx}
                className="bg-[#E6F0FA] p-6 rounded-2xl shadow hover:shadow-md transition duration-300"
              >
                <div className="flex items-start gap-3">
                  <Icon className="w-9 h-9 text-[#0274D9] mt-1" />
                  <p className="text-gray-800">{text}</p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section> */}
      {/*end build for Veterinary */}

      {/*Streamline your operations sectons start  */}
      <section className="container mx-auto pb-5 md:pb-10 pt-12 px-4 text-center rounded-[20px]">
        <div className="max-w-6xl mx-auto">
          <div className="text-center mb-10">
            <h2 className="text-2xl md:text-4xl">Built For Veterinary Hospitals</h2>
            <p className="text-xl py-2">Everything is in sync</p>
          </div>
          <div className="text-center">
            <p className="mt-5 w-full md:w-[80%] mx-auto ">Streamline your operations, accelerate business growth with a custom website and results-driven digital marketing. Benefit from a modern VoIP phone system, and a comprehensive client engagement platform. Improve customer satisfaction and provide timely, compassionate care with MyVetHub tools.</p>
            <p className="mt-6 md:mt-10">
              <HoverButton
                text="Book a Demo"
                link="/contact"
                className=""
              />
            </p>
          </div>
        </div>
      </section>
      {/*Streamline your operations sectons end */}

      {/* feature product card */}
      <section className="px-4 py-5">
        <div className="container mx-auto space-y-5">
          {featuresData?.map((item, index: number) => {
            const Icon = (titleIconMap as Record<string, React.ElementType>)[item.title];
            return <div
              key={index}
              className={`md:grid md:grid-cols-12 md:gap-x-12 py-5  items-center ${index % 2 === 0 ? "bg-gradient-to-l from-[#e4f2fe] to-[#fff3fe] rounded-[40px] pl-3" : "bg-white"
                }`}
            >
              {/* Text */}
              <div
                className={`md:col-span-6 ${index % 2 === 1 ? "order-2 md:order-2 md:col-start-7" : "order-2 md:order-1"
                  } text-black`}
              >
                <div className="xl:w-[90%]">
                  <h2 className="mb-8 text-xl sm:text-2xl md:text-3xl text-primary flex items-center gap-3">
                    {Icon && (
                      <span className={`inline-flex items-center justify-center w-10 h-10 rounded-full shadow-md  
                      ${index % 2 === 1 ? "bg-white text-blue-700" : "bg-blue-100 text-blue-700"
                        }
                      `}>
                        <Icon className="w-5 h-5" />
                      </span>
                    )}
                    {item.title}
                  </h2>

                  <div className="mb-6">
                    <p>
                      {item.boldDescription && (
                        <span className="font-semibold text-lg">
                          {item.boldDescription}
                        </span>
                      )}
                      {" "}
                      {item.description}
                    </p>
                  </div>

                  <ul className="pl-6 mb-6 space-y-2">
                    {item.features.map((feature, i) => (
                      <li key={i} className="flex gap-x-3">
                        {/* <span className="mt-0.5 size-5 flex justify-center items-center rounded-full bg-blue-50 text-blue-600  ">
                          <svg
                            className="shrink-0 size-3.5"
                            xmlns="http://www.w3.org/2000/svg"
                            width="24"
                            height="24"
                            viewBox="0 0 24 24"
                            fill="none"
                            stroke="currentColor"
                            strokeWidth="2"
                            strokeLinecap="round"
                            strokeLinejoin="round"
                          >
                            <polyline points="20 6 9 17 4 12" />
                          </svg>
                        </span> */}
                        <span className="mt-2 w-2 h-2 bg-secondary rounded-full flex-shrink-0"></span>
                        <div className="grow">
                          <span className=" ">
                            {feature}
                          </span>
                        </div>
                      </li>
                    ))}
                  </ul>

                  {item.footerNote && (
                    <p className="font-semibold text-lg">{item.footerNote}</p>
                  )}

                  <div className="flex gap-2 flex-wrap justify-center md:justify-start mt-6">
                    {item.ctas.map((cta, i) => (
                      <HoverButton
                        key={i}
                        text={cta.label}
                        link={cta.link}
                        className={` ${i % 2 === 1
                          ? 'bg-yellow-500 border-yellow-500 !text-black'
                          : ''
                          }`}
                      />
                    ))}
                  </div>
                </div>
              </div>

              {/* Image */}
              {/* <div className={`md:col-span-6 relative h-[300px] sm:h-[350px] md:h-[550px] w-full lg:w-[600px] ${index % 2 === 1 ? "order-1 md:order-1 md:col-start-1" : "order-1 md:order-2"
                }`}>
                <div className="absolute inset-5 rounded-lg overflow-hidden">
                  <Image
                    src={item.imageUrl}
                    alt={item.title}
                    width={600}
                    height={400}
                    // fill
                    className="rounded-lg object-cover inset-5"
                  />
                </div>
              </div> */}

              <div
                className={`md:col-span-6 relative h-[300px] sm:h-[350px] md:h-[450px] lg:h-[550px] w-full ${index % 2 === 1
                  ? "order-1 md:order-1 md:col-start-1"
                  : "order-1 md:order-2"
                  }`}
              >
                <div className="absolute inset-5 rounded-lg overflow-hidden">
                  <Image
                    src={item.imageUrl}
                    alt={item.title}
                    fill
                    className="object-cover w-full h-full rounded-lg"
                  />
                </div>
              </div>

            </div>
          })}
          {/* {
        "title": "Online Payments & Real-Time Reporting",
        "boldDescription": "",
        "description": "Simple payments. Clear insights. Zero confusion. Payments, revenue tracking, and visit data all flow through one system—making life easier for everyone involved in pet care.",
        "features": [
            "Secure and Contactless payments directly through the app",
            "Automated reminders reduce delays and missed invoices",
            "Financial dashboards show revenue, appointments, and service trends in real time",
            "Clear transaction history and receipts for owners, doctors, and staff",
            "Export options simplify accounting, reporting, and tax filing"
        ],
        "footerNote": "From quick checkouts to big-picture insights, everything stays organized.",
        "ctas": [
            {
                "label": "Get Paid On Time",
                "link": "/contact"
            },
            {
                "label": "Book A Free Demo",
                "link": "/contact"
            }
        ],
        "imageUrl": "/images/payment1.png"
    }, */}

        </div>
      </section>

      {/* Everything You Need section */}
      {/* <section className="bg-linear-to-r from-[#f7f7f7] to-[#f7f7f7] rounded-xl py-16">
        <div className="w-full px-4 mx-auto text-center">
          <h2 className="mb-3 text-xl sm:text-2xl md:text-3xl text-black">
            <span className="italic font-medium">Everything</span> You Need to{" "}
            <br />
            <span className="text-primary">Grow Your Veterinary Practice</span>
          </h2>
        </div>
        <div className="container mx-auto  px-4 pb-15 pt-5 gap-y-6 grid lg:grid-cols-2 gap-4">
          <div className="flex flex-col sm:flex-row gap-3 bg-white p-5 border border-gray-100 shadow-md rounded-xl overflow-hidden">
            <div className="w-full md:w-[30%]">
              <Image
                width={0}
                height={0}
                className=" w-full h-auto object-cover object-center transition duration-50"
                loading="lazy"
                src="/images/NLP.png"
                alt={""}
              />
            </div>

            <div className="w-full md:w-[70%] pl-4 text-black">
              <p className="text-lg md:text-xl font-medium">
                AI-Based NLP Support
              </p>

              <p className=" pt-4">
                Our AI voice and text system makes it easy to manage scheduling,
                answer FAQs, and route calls—saving your team time and keeping
                communication efficient. It&apos;s always on, super fast, and
                adapts to your workflow.
              </p>

              <div className="mt-6 group inline-flex cursor-pointer items-center gap-x-1 font-medium text-primary ">
                Learn more
                <svg
                  className="shrink-0 size-4 transition ease-in-out group-hover:translate-x-1 group-focus:translate-x-1"
                  xmlns="http://www.w3.org/2000/svg"
                  width="24"
                  height="24"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                >
                  <path d="m9 18 6-6-6-6" />
                </svg>
              </div>
            </div>
          </div>
          <div className="flex flex-col sm:flex-row gap-3 bg-white p-5 border border-gray-100 shadow-md rounded-xl overflow-hidden">
            <div className="w-full md:w-[30%]">
              <Image
                width={0}
                height={0}
                className=" w-full h-auto object-cover object-center transition duration-50"
                loading="lazy"
                src="/images/smartMarketingTool.png"
                alt={""}
              />
            </div>
            <div className="w-full md:w-[70%] pl-4 text-black">
              <p className="text-lg md:text-xl font-medium">
                Smarter Marketing Tools
              </p>

              <p className=" pt-4">
                Boost visibility and grow your brand with social media
                campaigns, SEO-rich websites, Google and Meta ads, blog content,
                print packages, and optimized directory listings - all tailored
                to attract and retain pet owners.
              </p>

              <div className="mt-6 group inline-flex cursor-pointer items-center gap-x-1 font-medium text-primary ">
                Learn more
                <svg
                  className="shrink-0 size-4 transition ease-in-out group-hover:translate-x-1 group-focus:translate-x-1"
                  xmlns="http://www.w3.org/2000/svg"
                  width="24"
                  height="24"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                >
                  <path d="m9 18 6-6-6-6" />
                </svg>
              </div>
            </div>
          </div>
          <div className="flex flex-col sm:flex-row gap-3 bg-white p-5 border border-gray-100 shadow-md rounded-xl overflow-hidden">
            <div className="w-full md:w-[30%]">
              <Image
                width={0}
                height={0}
                className=" w-full h-auto object-cover object-center transition duration-50"
                loading="lazy"
                src="/images/effortlessMgt.png"
                alt={""}
              />
            </div>

            <div className="w-full md:w-[70%] pl-4 text-black">
              <p className="text-lg md:text-xl font-medium">
                Effortless Appointment Management
              </p>

              <p className=" pt-4">
                Offer flexible booking with telemedicine, web scheduling, and AI
                voice assistants. Reminders via text, email, and calls reduce
                no-shows and keep your day running smoothly.
              </p>

              <div className="mt-6 group inline-flex cursor-pointer items-center gap-x-1 font-medium text-primary ">
                Learn more
                <svg
                  className="shrink-0 size-4 transition ease-in-out group-hover:translate-x-1 group-focus:translate-x-1"
                  xmlns="http://www.w3.org/2000/svg"
                  width="24"
                  height="24"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                >
                  <path d="m9 18 6-6-6-6" />
                </svg>
              </div>
            </div>
          </div>
          <div className="flex flex-col sm:flex-row gap-3 bg-white p-5 border border-gray-100 shadow-md rounded-xl overflow-hidden">
            <div className="w-full md:w-[30%]">
              <Image
                width={0}
                height={0}
                className=" w-full h-auto object-cover object-center transition duration-50"
                loading="lazy"
                src="/images/smartCommunication.png"
                alt={""}
              />
            </div>
            <div className="w-full md:w-[70%] pl-4 text-black">
              <p className="text-lg md:text-xl font-medium">
                Smart Communication Tools
              </p>

              <p className=" pt-4">
                Equip your team with voice-enabled phones and connect with pet
                parents through a mobile app that handles messages, forms,
                payments, and reminders in one place.
              </p>

              <div className="mt-6 group inline-flex cursor-pointer items-center gap-x-1 font-medium text-primary ">
                Learn more
                <svg
                  className="shrink-0 size-4 transition ease-in-out group-hover:translate-x-1 group-focus:translate-x-1"
                  xmlns="http://www.w3.org/2000/svg"
                  width="24"
                  height="24"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                >
                  <path d="m9 18 6-6-6-6" />
                </svg>
              </div>
            </div>
          </div>
          <div className="flex flex-col sm:flex-row gap-3 bg-white p-5 border border-gray-100 shadow-md rounded-xl overflow-hidden">
            <div className="w-full md:w-[30%]">
              <Image
                width={0}
                height={0}
                className=" w-full h-auto object-cover object-center transition duration-50"
                loading="lazy"
                src="/images/directBook.png"
                alt={""}
              />
            </div>

            <div className="w-full md:w-[70%] pl-4 text-black">
              <p className="text-lg md:text-xl font-medium">
                Direct Booking Made Easy
              </p>

              <p className=" pt-4">
                Let pet parents book instantly via your app or site with
                built-in deposits and automated reminders to keep schedules
                tight and no-shows low.
              </p>

              <div className="mt-6 group inline-flex cursor-pointer items-center gap-x-1 font-medium text-primary ">
                Learn more
                <svg
                  className="shrink-0 size-4 transition ease-in-out group-hover:translate-x-1 group-focus:translate-x-1"
                  xmlns="http://www.w3.org/2000/svg"
                  width="24"
                  height="24"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                >
                  <path d="m9 18 6-6-6-6" />
                </svg>
              </div>
            </div>
          </div>
          <div className="flex flex-col sm:flex-row gap-3 bg-white p-5 border border-gray-100 shadow-md rounded-xl overflow-hidden">
            <div className="w-full md:w-[30%]">
              <Image
                width={0}
                height={0}
                className=" w-full h-auto object-cover object-center transition duration-50"
                loading="lazy"
                src="/images/payments.png"
                alt={""}
              />
            </div>
            <div className="w-full md:w-[70%] pl-4 text-black">
              <p className="text-lg md:text-xl font-medium">
                Simple & Secure Payments
              </p>

              <p className=" pt-4">
                Speed up checkout with our secure POS system and web
                payments—easy for clients, efficient for staff, and built to
                reduce billing delays.
              </p>

              <div className="mt-6 group inline-flex cursor-pointer items-center gap-x-1 font-medium text-primary ">
                Learn more
                <svg
                  className="shrink-0 size-4 transition ease-in-out group-hover:translate-x-1 group-focus:translate-x-1"
                  xmlns="http://www.w3.org/2000/svg"
                  width="24"
                  height="24"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                >
                  <path d="m9 18 6-6-6-6" />
                </svg>
              </div>
            </div>
          </div>
        </div>
      </section> */}

      {/* Wondering Sections */}
      {/* <section>
        <div className="container mx-auto w-full px-4 py-16 text-center">
          <h2 className="text-xl sm:text-2xl md:text-3xl text-black">
            Wondering <em className="italic font-medium">How it Works?</em>
            <br />
            Getting Started is <span className="text-primary">Simple</span>
          </h2>

          <div className="flex flex-col md:flex-row items-center md:items-start justify-between  gap-y-20 mt-12 relative text-black">
            <div className="flex flex-col items-center w-3/4 sm:w-1/3 px-2">
              <div className="text-4xl md:text-5xl lg:text-7xl font-semibold text-black mb-2">
                1
              </div>
              <h3 className="font-medium text-lg">Book a Demo</h3>
              <p className=" mt-1">
                See how our solutions fit into your workflow.
              </p>
            </div>

            <div className="md:absolute rotate-90 md:rotate-0 left-1/3 md:top-4 lg:top-7 transform translate-x-0 md:-translate-x-1/2  w-1/6">
              <svg
                className="w-full h-2 lg:h-4 text-gray-300"
                viewBox="0 0 100 10"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <line
                  x1="0"
                  y1="5"
                  x2="100"
                  y2="5"
                  stroke="currentColor"
                  strokeWidth="2"
                />

                <polyline
                  points="94,0 102,5 94,10"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                />
              </svg>
            </div>

            <div className="flex flex-col items-center w-3/4 sm:w-1/3 px-2">
              <div className="text-4xl md:text-5xl lg:text-7xl font-semibold text-black mb-2">
                2
              </div>
              <h3 className="font-medium text-lg">Seamless Setup</h3>
              <p className=" mt-1">We handle the tech, so you don’t have to.</p>
            </div>

            <div className="md:absolute rotate-90 md:rotate-0 left-2/3 md:top-4 lg:top-7 transform translate-x-0 md:-translate-x-1/2 w-1/6">
              <svg
                className="w-full h-2 lg:h-4 text-gray-300"
                viewBox="0 0 100 10"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <line
                  x1="0"
                  y1="5"
                  x2="100"
                  y2="5"
                  stroke="currentColor"
                  strokeWidth="2"
                />

                <polyline
                  points="94,0 102,5 94,10"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                />
              </svg>
            </div>

            <div className="flex flex-col items-center w-3/4 sm:w-1/3 px-2">
              <div className="text-4xl md:text-5xl lg:text-7xl font-semibold text-black mb-2">
                3
              </div>
              <h3 className="font-medium text-lg">Focus on Patients</h3>
              <p className=" mt-1">
                Let automation and smart tools take care of the rest.
              </p>
            </div>

            <div className="absolute right-0 top-0 hidden xl:block transform -translate-x-5 -translate-y-25 ">
              <svg
                className="w-40 h-40 text-primary"
                viewBox="0 0 110 90"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <line
                  x1="0"
                  y1="80"
                  x2="95"
                  y2="25"
                  stroke="currentColor"
                  strokeWidth="3"
                  strokeDasharray="5,4"
                />

                <path
                  d="M93,37 L94,25 L82,22"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2.5"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                />
              </svg>
            </div>
          </div>

          <div className="mt-18">
            <HoverButton
              text="Schedule Your Demo Now"
              link="/contact"
              className="py-3"
            />
          </div>
        </div>
      </section> */}

      {/* Real Stories sections */}
      <TestimonialsSection />

      {/*start Catch the Latest Buzz section */}
      {/* bg-gradient-to-b from-[#EAF4FE] to-[#F6FAFE] */}
      <section className="bg-secondary text-black py-10 md:py-15 px-4">
        <div className="container mx-auto">
          <h2 className="text-white text-2xl md:text-4xl mb-3 text-center md:mb-2 text-center">
            Catch the Latest Buzz
          </h2>
          {/* <HoverLinkButton text="Explore More Resources" link="/blog" /> */}

          <div className="grid grid-cols-1 gap-4 mt-10 md:grid-cols-2 xl:grid-cols-4">
            {limitedBlogs.map(({ id, slug, title, subtitle, image }) => (
              <div key={id} className="bg-white flex flex-col h-full shadow-xl pb-5 overflow-hidden rounded-lg group">
                <div className="relative overflow-hidden rounded-lg">
                  <Image
                    className="object-cover object-center w-full h-64 rounded-lg lg:h-80 transition-transform duration-500 ease-in-out group-hover:scale-105"
                    src={image}
                    alt={`blogImage_${id}`}
                    width={500}
                    height={320}
                  />
                </div>

                {/* Make content area grow to equal height */}
                <div className="flex flex-col flex-grow px-4">
                  <h1 className="mt-6 font-medium text-lg md:h-20">{title}</h1>
                  {/* <hr className="w-32 my-6 text-black" /> */}
                  <p className="flex-grow line-clamp-3 my-6">{subtitle}</p>{" "}
                  <Link href={`/blog/${slug}`}>
                    <div className="group mt-5 inline-flex cursor-pointer items-center gap-x-1 font-medium text-primary">
                      Read More
                      <svg
                        className="shrink-0 size-4 transition ease-in-out group-hover:translate-x-1 group-focus:translate-x-1"
                        xmlns="http://www.w3.org/2000/svg"
                        width="24"
                        height="24"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                        strokeWidth="2"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                      >
                        <path d="m9 18 6-6-6-6" />
                      </svg>
                    </div>
                  </Link>
                </div>
              </div>
            ))}
          </div>

          <div className="flex justify-center pt-10">
            <HoverButton
              text="Explore More Resources"
              link="/blog"
              className="bg-yellow-500 border-yellow-500 !text-black"
            />
            {/* <Link className="text-black flex items-center" href={"/blog"}>
              <span className="mr-1">Explore More Resources</span>
              <svg
                stroke-linecap="round"
                className=""
                strokeWidth="1.5"
                aria-hidden="true"
                viewBox="0 0 10 10"
                height="11"
                width="15"
                stroke="currentColor"
                fill="none"
              >
                <path
                  stroke-linecap="round"
                  d="M0 5h7"
                  className="opacity-0 transition group-hover:opacity-100"
                ></path>
                <path
                  stroke-linecap="round"
                  d="M1 1l4 4-4 4"
                  className="transition group-hover:translate-x-[3px]"
                ></path>
              </svg>
            </Link> */}
          </div>
        </div>
      </section>
      {/*End Catch the Latest Buzz section */}

      {/* Questions Sections Start */}
      {/* <section className="bg-gradient-to-l from-[#e4f2fe] to-[#fff3fe] pt-10 pb-15 px-4 sm:px-10 md:px-20">
        <div className="max-w-5xl mx-auto">
          <div className="text-center mb-10">
            <h2 className="text-2xl md:text-4xl">
              Questions You Might Be Asking…
            </h2>
          </div>

          <div className="space-y-6">
            {faqList?.slice(0, 3)?.map((section, index) => (
              <div
                key={index}
                className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden transition-all duration-300"
              >
                <button
                  onClick={() => toggleAccordion(index)}
                  className="w-full px-6 py-5 text-left flex items-center justify-between bg-gray-50 cursor-pointer"
                >
                  <span
                    className={`font-medium md:text-lg text-primary transition-colors duration-300 ${openIndex === index ? "text-[#0085D0]" : ""
                      }`}
                  >
                    {section.question}
                  </span>
                  <ChevronDown
                    className={`w-5 h-5 text-primary transition-transform duration-300 ${openIndex === index ? "rotate-180" : ""
                      }`}
                  />
                </button>

                <div
                  className={`transition-all duration-300 ease-in-out px-6 overflow-hidden ${openIndex === index
                    ? "max-h-screen opacity-100 pb-6"
                    : "max-h-0 opacity-0 pb-0"
                    }`}
                >
                  <p className="text-gray-700 text-lg leading-relaxed pt-2">
                    {section.answer}
                  </p>
                </div>
              </div>
            ))}
          </div>

          <div className="text-center mt-10">
            <HoverButton
              text="View More FAQs"
              link="/faqs"
              className=""
            />
          </div>
        </div>
      </section> */}
      <section className="bg-gradient-to-l from-[#e4f2fe] to-[#fff3fe] pt-10 pb-10 md:pb-15 px-4 sm:px-10 md:px-20">
        <div className="max-w-5xl mx-auto">
          <div className="text-center mb-10">
            <h2 className="text-2xl md:text-4xl">
              Questions You Might Be Asking…
            </h2>
          </div>

          <div className="space-y-6">
            {faqList?.slice(0, 3)?.map((section, index) => (
              <div
                key={index}
                className="overflow-hidden transition-all duration-300"
              >
                <button
                  onClick={() => toggleAccordion(index)}
                  className="w-full px-6 py-5 text-left flex items-center justify-between cursor-pointer border-b-2 border-main"
                >
                  <span
                    className={`font-medium md:text-xl text-primary transition-colors duration-300 ${openIndex === index ? "text-primary" : ""
                      }`}
                  >
                    {section.question}
                  </span>
                  <ChevronDown
                    className={`flex-shrink-0 bg-secondary rounded-full w-7 h-7 text-white transition-transform duration-300 ${openIndex === index ? "rotate-180" : ""
                      }`}
                  />
                </button>

                <div
                  className={`transition-all duration-300 ease-in-out px-6 overflow-hidden ${openIndex === index
                    ? "max-h-screen opacity-100 pb-6"
                    : "max-h-0 opacity-0 pb-0"
                    }`}
                >
                  <p className="text-gray-700 text-lg leading-relaxed pt-2">
                    {section.answer}
                  </p>
                </div>
              </div>
            ))}
          </div>

          <div className="text-center mt-10">
            <HoverButton
              text="View More FAQs"
              link="/faqs"
              className=""
            />
          </div>
        </div>
      </section>
      {/* Questions Sections end */}
    </div>
  );
};

export default LandingPage;
