import { Menutype } from "../models/common.model";

// constants/dropdown-menu.ts
export const productDropDownMenuItem: Menutype[] = [
  {
    label: "Website Plus",
    path: "/product/marketing",
    image: "/icons/web.png",
    description: "Boost your online presence with tailored marketing tools.",
  },
  {
    label: "Intelligent Telephony",
    path: "/product/phones",
    image: "/icons/phone.png",
    description: "Smart phone system integration to streamline communication.",
  },
  {
    label: "Smart Inventory",
    path: "/product/inventory",
    image: "/icons/asset.png",
    description: "Manage your stock efficiently with real-time tracking.",
  },
  {
    label: "Telemedicine",
    path: "/product/telemedicine",
    image: "/icons/medical-app.png",
    description: "Consult patients remotely and securely.",
  },
  {
    label: "Appointment Manager",
    path: "/product/appointment-manager",
    image: "/icons/time-frame.png",
    description: "Efficiently handle scheduling and appointments.",
  },
  {
    label: "Communication",
    path: "/product/communication",
    image: "/icons/discussion.png",
    description: "Centralize and simplify client communications.",
  },
  {
    label: "Digital Forms",
    path: "/forms",
    image: "/icons/edit.png",
    description: "Save time and money spent on paper forms.",
  },
  {
    label: "AI Transcribe",
    path: "/ai-transcribe",
    image: "/icons/speech.png",
    description: "Convert speech into accurate text instantly with AI.",
  },
];

export const featureDropDownMenuItem: Menutype[] = [
  {
    label: "Communication Systems",
    path: "/",
    submenu: [
      { label: "Mobile APP", path: "/" },
      { label: "SMS", path: "/" },
      { label: "Appointment Bookings", path: "/" },
      { label: "Reminders & Confirmations", path: "/" },
    ],
  },
  {
    label: "Smart Telephones Systems",
    path: "/",
    submenu: [
      { label: "AI powered VOIP Phones", path: "/" },
      { label: "NLP Enables IVR", path: "/" },
      { label: "SMart Call Routing and Scheduling", path: "/" },
    ],
  },
  {
    label: "Appointment Manager",
    path: "/",
    submenu: [
      { label: "Real-Time Appointment Scheduling", path: "/" },
      { label: "Online payments", path: "/" },
      { label: "Telemedicine", path: "/" },
    ],
  },
];

export const resourcesDropDownMenuItem: Menutype[] = [
  // { label: "Photo Gallery", path: "/" },
  {
    label: "FAQ",
    path: "/faqs",
    image: "/icons/faq.png",
    description: "Frequently Asked Questions",
  },
  // {
  //   label: "Forms",
  //   path: "/forms",
  //   image: "/icons/edit.png",
  //   description: "Visit our forms",
  //   },
  {
    label: "Blogs",
    path: "/blog",
    image: "/icons/blog.png",
    description: "Articles written by our staff to help your business thrive.",
  },
];
