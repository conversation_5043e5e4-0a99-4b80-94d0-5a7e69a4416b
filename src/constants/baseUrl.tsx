// utils/getDynamicConfig.ts

export const getDynamicConfig = () => {
  const hostEnvironment = process.env.NEXT_PUBLIC_API_BASE!;

  if (typeof window === "undefined") {
    // SSG or SSR build time – return default values
    return {
      baseUrl: hostEnvironment,
      apiBaseUrl: "https://uat-api.myvethub.com",
    };
  }

  const hostname = window.location.hostname?.toLowerCase();

  if (["www.myvethub.com", "myvethub.com"].includes(hostname)) {
    return {
      baseUrl: hostEnvironment,
      apiBaseUrl: "https://api.myvethub.com",
    };
  }

  // Fallback to QA/Dev
  return {
    baseUrl: hostEnvironment,
    apiBaseUrl: "https://uat-api.myvethub.com",
  };
};
