import dayjs from "dayjs";
import timezone from "dayjs/plugin/timezone";
import utc from "dayjs/plugin/utc";
dayjs.extend(utc);
dayjs.extend(timezone);

export const getTimezoneOffsetFromDateTimeString = (dateTime: string) => {
  const regex = /([+\-]\d{2}:\d{2})/;
  const match = dateTime.match(regex);
  return match ? match[0] : "";
};

export const getMinutesFromOffset = (offsetStr: string) => {
  const [hours, minutes] = offsetStr.split(":").map(Number);
  let totalOffsetMinutes;
  if (hours < 0) {
    totalOffsetMinutes = hours * -1 * 60 + minutes;
    return -totalOffsetMinutes;
  }
  totalOffsetMinutes = hours * 60 + minutes;

  return totalOffsetMinutes;
};

export const getFormatedDate = ({
  date,
  formate = "MM/DD/YYYY",
  tz,
}: {
  date: Date | dayjs.Dayjs;
  formate?: string;
  tz?: string;
}) => {
  if (tz) {
    const tzOffsetInMinutes = getMinutesFromOffset(tz);
    return dayjs(date).utcOffset(tzOffsetInMinutes).format(formate);
  }
  return dayjs(date).format(formate);
};

export const getFormatedTime = ({
  date,
  formate = "hh:mm A",
  tz,
}: {
  date: Date | dayjs.Dayjs;
  formate?: string;
  tz?: string;
}) => {
  if (tz) {
    const tzOffsetInMinutes = getMinutesFromOffset(tz);
    return dayjs(date).utcOffset(tzOffsetInMinutes).format(formate);
  }

  return dayjs(date).format(formate);
};

export function cleanPhoneNumber(phone?: string): string | undefined {
  if (!phone) return undefined;
  return phone.replace(/[()\s-]/g, "");
}

