import fs from "fs/promises"; // Use promises version
import path from "path";

interface Post {
  id: number;
  slug: string;
  title: string;
  subtitle: string;
  content: string;
  image: string;
}

export const getBlogData = async (): Promise<Post[]> => {
  const filePath = path.join(
    process.cwd(),
    "src",
    "constants",
    "blogs",
    "blogs.json"
  );
  const jsonData = await fs.readFile(filePath, "utf-8");
  return JSON.parse(jsonData);
};
