// import { apiBaseUrl } from "@/constant/baseUrl";

import { getDynamicConfig } from "@/constants/baseUrl";

const { apiBaseUrl } = getDynamicConfig(); // runtime-safe

export const CURRENT_ENVIRONMENT: string =
  process.env.NODE_ENV || "development";
type VariableType = {
  API_BASE: string;
};

type ConfigType = {
  [key: string]: VariableType;
};

// const BASE_URL = typeof window !== "undefined" ? window?.location?.origin : "";
// const LOCAL_ENV = "http://localhost:3001"; 
const LOCAL_ENV = "https://uat-api.myvethub.com";

const CONFIG: ConfigType = {
  test: {
    API_BASE: LOCAL_ENV,
  },
  development: {
    API_BASE: LOCAL_ENV,
  },
  production: {
    API_BASE: apiBaseUrl,
  },
};
export const ENV_VARIABLES = CONFIG[CURRENT_ENVIRONMENT];
