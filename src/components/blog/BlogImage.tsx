import Image from "next/image";

export interface BlogImageProps {
  image: string;
  label: string;
}

export const BlogImage = ({ image, label }: BlogImageProps) => {
  return (
    <div className="relative w-full mb-10">
      <div className="relative w-full h-[250px] sm:h-[350px] md:h-[450px] lg:h-[550px]">
        <Image
          src={image}
          alt={label}
          fill
          priority
          className="object-cover rounded-2xl shadow-lg"
        />
      </div>
    </div>
  );
};
