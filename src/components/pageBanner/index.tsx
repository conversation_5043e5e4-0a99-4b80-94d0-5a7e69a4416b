import React from "react";

interface PageBannerProps {
    title: string;
    subtext?: string;
    homeText?: string;
    imageUrl: string;
}

const PageBanner: React.FC<PageBannerProps> = ({
    title,
    subtext,
    homeText,
    imageUrl,
}) => {
    return (
        <div
            className="w-full min-h-[300px] bg-cover bg-center relative flex items-center"
            style={{ backgroundImage: `url(${imageUrl})` }}
        >
            {/* Optional overlay if needed for text contrast */}
            {/* <div className="absolute inset-0 bg-white/70"></div> */}

            <div className="relative z-10 w-full container mx-auto px-4 max-w-4xl">
                <div className="max-w-3xl">
                    <h1 className="text-3xl md:text-4xl font-bold text-black">{title}</h1>
                    {homeText && (
                        <p className="text-lg text-gray-700 mt-2">{homeText}</p>
                    )}
                    {subtext && <p className="text-sm text-gray-500 mt-1">{subtext}</p>}
                </div>
            </div>
        </div>
    );
};

export default PageBanner;
