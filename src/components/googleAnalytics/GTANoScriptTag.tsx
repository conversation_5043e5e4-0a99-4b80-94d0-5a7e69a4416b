// components/GoogleTagManagerNoScript.tsx
"use client";

import { useEffect, useState } from "react";
import Cookies from "js-cookie";

export default function GoogleTagManagerNoScript() {
  const [consent, setConsent] = useState<string | null>(
    Cookies.get("cookie_consent") || null
  );

  useEffect(() => {
    const interval = setInterval(() => {
      const storedConsent = Cookies.get("cookie_consent") || null;
      if (storedConsent !== consent) {
        setConsent(storedConsent);
      }
    }, 1000);

    return () => clearInterval(interval);
  }, [consent]);

  if (consent !== "accepted") {
    return null;
  }

  /* Google Tag Manager (noscript) */

  return (
    <noscript>
      <iframe
        src="https://www.googletagmanager.com/ns.html?id=GTM-5KW66M55"
        height="0"
        width="0"
        style={{ display: "none", visibility: "hidden" }}
      />
    </noscript>
  );
}
