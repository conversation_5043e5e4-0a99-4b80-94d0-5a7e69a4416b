"use client";
import React, { useEffect, useState } from "react";
import Script from "next/script";
import Cookies from "js-cookie";

const GoogleAnalytics = () => {
  const [consent, setConsent] = useState<string | null>(
    Cookies.get("cookie_consent") || null
  );

  useEffect(() => {
    const interval = setInterval(() => {
      const storedConsent = Cookies.get("cookie_consent") || null;
      if (storedConsent !== consent) {
        setConsent(storedConsent);
      }
    }, 1000);

    return () => clearInterval(interval);
  }, [consent]);

  if (consent !== "accepted") {
    return null;
  }
  return (
    <>
  
      <Script
        async
        src="https://www.googletagmanager.com/gtag/js?id=G-PJVWCL7446"
      />
      <Script id="" strategy="lazyOnload">
        {`
          window.dataLayer = window.dataLayer || [];
          function gtag(){dataLayer.push(arguments);}   
          gtag('js', new Date());   
          
          gtag('config', 'G-PJVWCL7446');
        `}
      </Script>
      {/* <!-- Google tag (gtag.js) -->  */}
    </>
  );
};

export default GoogleAnalytics;
