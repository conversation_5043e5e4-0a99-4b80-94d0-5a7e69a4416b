// components/CookieBanner.tsx
"use client";

import { useEffect, useState } from "react";
import Cookies from "js-cookie";
import Link from "next/link";
import { X } from "lucide-react";

export default function CookieBanner() {
  const [visible, setVisible] = useState(false);
  const [animate, setAnimate] = useState(false);

  useEffect(() => {
    const consent = Cookies.get("cookie_consent");
    if (!consent) {
      setVisible(true);
      setTimeout(() => setAnimate(true), 50); // trigger animation after mount
    }
  }, []);

  const acceptCookies = () => {
    Cookies.set("cookie_consent", "accepted", { expires: 365 }); // 1 year
    // window.location.reload(); // 🔑 force reload so analytics mounts immediately
    setAnimate(false);
    setTimeout(() => setVisible(false), 500); // wait for animation
  };

  const rejectCookies = () => {
    Cookies.set("cookie_consent", "rejected", { expires: 7 }); // 7 days
    setAnimate(false);
    setTimeout(() => setVisible(false), 500);
  };

  const closeBanner = () => {
    setAnimate(false);
    setTimeout(() => setVisible(false), 500);
  };

  if (!visible) return null;

  return (
    <div
      className={`fixed bottom-0  w-full bg-white border border-t-gray-300  text-black py-3 px-6 z-50 transform transition-transform duration-500 ease-in-out ${
        animate ? "translate-y-0" : "translate-y-full"
      }`}
    >
      {/* Text */}
      <div className="flex flex-col md:flex-row justify-center items-center gap-2">
        <p className="text-sm text-center md:text-left">
          We use cookies to enhance your experience. By continuing to use our
          site, you agree to our
          <Link
            href="/privacy-policy"
            className="text-primary hover:underline ml-1 font-semibold"
          >
            Privacy Policy
          </Link>{" "}
          &
          <Link
            href="/cookie-policy"
            className="text-primary hover:underline ml-1 font-semibold"
          >
            Cookie Policy
          </Link>
          .
        </p>

        {/* Buttons */}
        <div className="flex gap-2 mt-2 md:mt-0 md:ml-4">
          <button
            onClick={acceptCookies}
            className="bg-secondary cursor-pointer rounded-full tracking-wide w-30 px-4 py-2 text-sm font-bold text-white transition-all duration-300 ease-in-out transform active:scale-95"
          >
            Accept
          </button>
          <button
            onClick={rejectCookies}
            className="bg-gray-700 cursor-pointer rounded-full tracking-wide w-30 px-4 py-2 text-sm font-bold text-white transition-all duration-300 ease-in-out"
          >
            Reject
          </button>
        </div>
      </div>

      {/* Close Button */}
      <button
        onClick={closeBanner}
        aria-label="Close"
        className="absolute top-0 right-1 text-gray-500 hover:text-gray-900"
      >
        <X className="w-5 h-5 font-bold" />
      </button>
    </div>
  );
}
