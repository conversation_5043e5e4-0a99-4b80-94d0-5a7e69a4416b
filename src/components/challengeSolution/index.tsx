import {
  AlertTriangle,
  EarthLock,
  LaptopMinimal,
  LaptopMinimalCheck,
  Lightbulb,
  MonitorSmartphone,
  NotebookPen,
  NotepadTextDashed,
  PhoneCall,
  PhoneMissed
} from 'lucide-react';

const challenges = [
  { icon: <LaptopMinimal className="w-5 h-5" />, heading: 'Websites That Don’t Reflect Your Brand', subheading: 'Many veterinary websites look and feel the same—failing to capture the unique identity and values of your practice.' },
  { icon: <PhoneMissed className="w-5 h-5" />, heading: 'Missed Calls and Communication Gaps', subheading: 'Unanswered calls, unclear messages, and lack of timely updates frustrate both your team and your clients.' },
  { icon: <NotepadTextDashed className="w-5 h-5" />, heading: 'Inventory Compliance Issues', subheading: 'Manual logs and outdated systems make it hard to maintain compliance, especially when dealing with controlled substances.' },
  { icon: <EarthLock className="w-5 h-5" />, heading: 'Limited Access for Remote Pet Owners', subheading: 'Geography should not be a barrier to care, yet many practices lack the tools to connect with clients in remote areas.' }
  // { icon: <CreditCard className="w-5 h-5" />, text: 'Inefficient Payment Systems' },
  // { icon: <Monitor className="w-5 h-5" />, text: 'Website doesn’t reflect brand' },
  // { icon: <TrendingDown className="w-5 h-5" />, text: 'Low visibility to potential clients' },
];

const solutions = [
  { icon: <LaptopMinimalCheck className="w-5 h-5" />, heading: 'Custom-Built Veterinary Websites That Reflect Your Brand', subheading: 'We design mobile-optimized, modern websites tailored to your practice’s voice and values—building trust from the first click.' },
  { icon: <PhoneCall className="w-5 h-5" />, heading: 'AI-Powered Communication Tools', subheading: 'Stay connected with clients using our intelligent VoIP and SMS platform—reducing missed calls and delivering consistent updates.' },
  { icon: <NotebookPen className="w-5 h-5" />, heading: 'Seamless Controlled Drug Inventory Management', subheading: 'Track, manage, and audit your inventory—especially controlled drugs—with accuracy, automation, and peace of mind.' },
  { icon: <MonitorSmartphone className="w-5 h-5" />, heading: 'Telemedicine That Expands Your Reach', subheading: 'Offer virtual consultations to serve pet owners beyond your clinic’s walls—making care accessible anytime, anywhere.' }
  // { icon: <DollarSign className="w-5 h-5" />, text: 'Integrated online payments with auto-generated invoices' },
  // { icon: <Monitor className="w-5 h-5" />, text: 'Custom-built, mobile-friendly veterinary websites' },
  // { icon: <Megaphone className="w-5 h-5" />, text: 'Marketing tailored to a veterinary clinic' },
];

export default function ChallengeSolutionSection() {
  // const [loaded, setLoaded] = useState(false);
  // const [cycle, setCycle] = useState(0);

  // useEffect(() => {
  //   const interval = setInterval(() => {
  //     setLoaded(false);
  //     setTimeout(() => setLoaded(true), 100);
  //     setCycle(prev => prev + 1);
  //   }, 5000);
  //   return () => clearInterval(interval);
  // }, []);

  // const cardClass =
  //   'p-5 border border-main  rounded-md shadow-sm bg-primary hover:!bg-[#E6F0FA] text-black ease-out transform text-xs flex items-start gap-3 h-[55px] w-full';
  // bg-[#E6F0FA]
  return (
    <section className="container mx-auto rounded-[20px] py-10 md:py-15 px-4 sm:px-8 lg:px-20 bg-gradient-to-l from-[#e4f2fe] to-[#fff3fe] relative overflow-hidden">
      <h2 className="text-2xl md:text-4xl text-center mb-5">
        Solutions That Cover Every Need of Your Practice
      </h2>
      <p className="text-center text-lg md:text-xl mb-10 text-md">
        Each solution solves a real problem—without added steps
      </p>

      <div className="container mx-auto sm:px-4 md:px-0 lg:px-20">
        <div className="relative flex flex-wrap md:flex-nowrap gap-6 items-start">
          {/* Challenges */}
          <div className="w-full md:w-[45%] flex flex-col gap-6 items-start md:items-end pr-4">
            <div className={`flex items-center gap-2 text-lg font-semibold text-black transition-opacity duration-700 opacity-100`}>
              <span className=''>Challenges We Solve</span><AlertTriangle className="w-5 h-5" />
            </div>
            {challenges.map((item, i) => (
              <div key={i} className="bg-[#FFECEC] min-h-[100px] xl:min-h-auto w-full justify-between md:justify-end max-w-xl flex items-center gap-3 rounded-sm px-4 py-5 shadow text-left md:text-right border-l-4 border-red-500">
                <div className="text-sm text-gray-700 font-semibold">{item.heading}</div>
                <span className="bg-red-500 rounded-full p-2 inline-block">
                  <span className="text-white">{item.icon}</span>
                </span>
              </div>
            ))}
          </div>

          {/* Center Vertical Line */}
          <div className="hidden md:grid place-items-center w-full md:w-[10%] mt-10 ">
            <div className="w-1 bg-gray-300 h-full absolute left-1/2 transform -translate-x-1/2"></div>
            {challenges.map((_, i) => (
              <div key={i} className="w-5 h-5 bg-yellow-500 border-4 border-white rounded-full z-10 my-13 xl:my-10"></div>
            ))}
          </div>

          {/* Solutions */}
          <div className="w-full md:w-[45%] flex flex-col gap-6 items-start md:pl-4 mt-4 md:mt-0">
            <div className={`flex items-center gap-2 text-lg font-semibold text-black transition-opacity duration-700 opacity-100`}>
              <Lightbulb className="w-5 h-5" />  <span className=''>How We Help</span>
            </div>
            {solutions.map((item, i) => (
              <div key={i} className="bg-primary min-h-[100px] xl:min-h-auto w-full justify-left flex items-center gap-3 max-w-xl rounded-sm px-4 py-5 shadow border-r-4 border-main">
                <span className="bg-secondary rounded-full p-2 inline-block">
                  <span className="text-white">{item.icon}</span>
                </span>
                <div className="text-sm text-gray-700 font-semibold">{item.heading}</div>
              </div>
            ))}
          </div>
        </div>

      </div>

    </section>
  );
}
