'use client'
import { useRouter } from "next/navigation";
import React from "react";

const HoverButton = ({
  text,
  link,
  className,
}: {
  text: string;
  link: string;
  className?: string;
}) => {
  const router = useRouter();
  return (
    <button
      onClick={() => router.push(link)}
      className={`${className} inline-block lg:px-6 py-2 px-3 cursor-pointer border-2 border-[#0274D9] rounded-full bg-[#0274D9] text-white transition-all hover:bg-white hover:text-[#0274D9] `}
    >
      {text}
    </button>
  );
};

export default HoverButton;
