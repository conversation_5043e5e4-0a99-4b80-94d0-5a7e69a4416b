import { ButtonPropsTypes } from "@/models/common.model";
import Link from "next/link";
import { memo } from "react";

const LinkButton = (props: ButtonPropsTypes) => {
  const { label, className, navigateLink, target, onClick } = props;
  return (
    <Link
      href={navigateLink || ""}
      className={`${className} bg-[#ffd014] tracking-wide cursor-pointer leading-4 py-3 px-4 md:px-6 rounded-md border-2 border-[#d6d6d6] shadow-[inset_0px_-2px_0px_1px_#0075d4] group hover:bg-[#0075d4] transition duration-300 ease-in-out`}
      aria-label={`${label}` || "Button"}
      target={target}
      onClick={onClick}>
      <span className="font-medium text-[#333] text-sm md:text-base group-hover:text-white">
        {label} <span className="sr-only">about {navigateLink}</span>
      </span>
    </Link>
  );
};
export default memo(LinkButton);
