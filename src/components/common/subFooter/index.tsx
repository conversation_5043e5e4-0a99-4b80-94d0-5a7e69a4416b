'use client'
import React from "react";
import HoverButton from "../hoverButton";

const SubFooter = () => {
  return (
    <section className="py-20 px-4 bg-linear-to-l from-[#f7f7f7] to-[#f7f7f7] rounded-xl text-center">
      <h3 className="text-xl sm:text-2xl md:text-3xl text-black">
        Less<span className="italic  font-medium"> Hassle</span>, More{" "}
        <span className="text-primary">Happy Clients</span>{" "}
      </h3>

      <p className="mt-5 w-[80%] mx-auto">Streamline your operations and accelerate business growth with a custom website and results-driven digital marketing. Benefit from 24/7 error-free booking, an integrated PIMS-VoIP phone system, and a comprehensive client engagement platform—complete with a mobile app for seamless connectivity.</p>

      <p className="mt-10">
        <HoverButton
          text="Try MyVetHub Today!"
          link="/contact"
          className="py-3 mb-2 sm:mb-0 ml-2"
        />

        {/* <HoverButton
          text="Talk To Expert"
          link="/"
          className="py-3 ml-2 !px-7  bg-black text-white border-black hover:bg-white hover:text-black"
        /> */}
      </p>
    </section>
  );
};

export default SubFooter;
