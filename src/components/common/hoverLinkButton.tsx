import { useRouter } from "next/navigation";
import React from "react";

const HoverLinkButton = ({
  text,
  link,
  className,
  textClassName,
}: {
  text: string;
  link: string;
  className?: string;
  textClassName?: string;
}) => {
  const router = useRouter();
  return (
    <button
      onClick={() => router.push(link)}
      className={`${className} group relative flex flex-row items-center justify-center gap-2 rounded-2xl px-4 py-1.5 font-medium shadow-[inset_20px_8px_20px_#8fdfff1f] transition-shadow duration-500 ease-out hover:shadow-[inset_0_-5px_10px_#8fdfff3f]`}
    >
      <span
        className={`${textClassName} inline animate-gradient whitespace-pre bg-gradient-to-r from-[#0274D9] via-[#b940ff] to-[#ffaa40] bg-[length:var(--bg-size)_100%] bg-clip-text text-transparent [--bg-size:300%] text-center`}
      >
        {text}
      </span>
      <svg
        stroke-linecap="round"
        className="text-[#9c40ff]"
        strokeWidth="1.5"
        aria-hidden="true"
        viewBox="0 0 10 10"
        height="11"
        width="15"
        stroke="currentColor"
        fill="none"
      >
        <path
          stroke-linecap="round"
          d="M0 5h7"
          className="opacity-0 transition group-hover:opacity-100"
        ></path>
        <path
          stroke-linecap="round"
          d="M1 1l4 4-4 4"
          className="transition group-hover:translate-x-[3px]"
        ></path>
      </svg>
    </button>
  );
};

export default HoverLinkButton;
