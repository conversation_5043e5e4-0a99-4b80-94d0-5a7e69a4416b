'use client';

import { useEffect, useState } from 'react';
import Image from 'next/image';
import { <PERSON>uo<PERSON>, <PERSON> } from 'lucide-react';

const testimonials = [
  {
    quote:
      "MyVetHub has allowed us to manage our missed and after hours calls in a much better way. We are able to connect with customers who would have gone somewhere else.",
    name: "<PERSON>, <PERSON>",
    role: "",
    clinic: "",
    image: "https://i.pravatar.cc/100?img=1",
  },
  {
    quote:
      "Our no-show rate dropped significantly as the reminders are timely and appropriate. The automated reminders really work well.",
    name: "<PERSON>, <PERSON>",
    role: "",
    clinic: "",
    image: "https://i.pravatar.cc/100?img=2",
  },
  {
    quote:
      "I liked the look and feel of emails from MyVetHub. They look nice and we the messaging is on the dot. It is easy to use.",
    name: "<PERSON>, <PERSON>",
    role: "",
    clinic: "",
    image: "https://i.pravatar.cc/100?img=3",
  },
  {
    quote:
      "We feel pretty good about our inventory of both schedule drugs and high-value items. Reporting to Cures portal has become so easy. We like the accountability and help with compliance that it brings.",
    name: "<PERSON><PERSON>, <PERSON>",
    role: "",
    clinic: "",
    image: "https://i.pravatar.cc/100?img=4",
  },
  {
    quote:
      "Telemedicine is popular with some of our senior owners and patients. They get same day appointments for minor concerns and only have to visit when it is necessary.",
    name: "Aman, S",
    role: "",
    clinic: "",
    image: "https://i.pravatar.cc/100?img=5",
  },
];

export default function TailsOfSuccess() {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [slidesPerView, setSlidesPerView] = useState(3);

  useEffect(() => {
    const handleResize = () => {
      const width = window.innerWidth;
      if (width < 640) {
        setSlidesPerView(1); // Mobile
      } else if (width < 1024) {
        setSlidesPerView(2); // Tablet
      } else {
        setSlidesPerView(3); // Desktop
      }
    };

    handleResize(); // initial
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  const totalSlides = Math.ceil(testimonials.length / slidesPerView);

  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentIndex((prev) => (prev + 1) % totalSlides);
    }, 4000);
    return () => clearInterval(interval);
  }, [totalSlides]);

  const handleDotClick = (index: number) => setCurrentIndex(index);

  return (
    <section className="py-16 bg-gradient-to-l from-[#e4f2fe] to-[#fff3fe] px-4 sm:px-10 md:px-16">
      <div className="text-center max-w-3xl mx-auto mb-14">
        <h2 className="text-2xl md:text-4xl text-black">
          Tails of Success
          {/* <span className="font-medium italic">Real Stories</span> from Veterinarians&nbsp;
          <span className="text-primary">Just Like You</span> */}
        </h2>
      </div>

      <div className="overflow-hidden">
        <div
          className="flex transition-transform duration-700 ease-in-out"
          style={{
            width: `${(100 / slidesPerView) * testimonials.length}%`,
            transform: `translateX(-${(100 / totalSlides) * currentIndex}%)`,
          }}
        >
          {testimonials.map((t, i) => (
            <div
              key={i}
              className="flex-shrink-0 px-2"
              style={{ width: `${100 / testimonials.length}%` }}
            >
              <div className="bg-white rounded-lg shadow-md px-6 py-8 relative text-black h-full">
                <Quote className="absolute top-6 left-6 text-gray-200 w-10 h-10" />
                <p className="mt-8 relative text-sm">{t.quote}</p>

                <div className="inline-flex items-center mt-6 space-x-3">
                  <Image
                    alt={`${t.name} photo`}
                    width={40}
                    height={40}
                    className="object-cover w-10 h-10 bg-gray-100 rounded-full shadow-inner ring-1 ring-gray-200"
                    src={t.image}
                  />
                  <div className="leading-tight">
                    <p className="text-sm font-semibold">{t.name}</p>
                    {(t.role&&t.clinic)&&<p className="text-xs">
                      {t.role}, {t.clinic}
                    </p>}
                    <div className="flex gap-0.5 mt-1 text-yellow-400">
                      {[...Array(5)].map((_, idx) => (
                        <Star
                          key={idx}
                          className="w-4 h-4 fill-yellow-400 stroke-yellow-400"
                        />
                      ))}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Dots */}
      <div className="flex justify-center mt-8 space-x-2">
        {Array.from({ length: totalSlides }).map((_, i) => (
          <button
            key={i}
            className={`w-3 h-3 rounded-full transition-all duration-300 cursor-pointer ${
              i === currentIndex ? 'bg-blue-600' : 'bg-gray-300'
            }`}
            onClick={() => handleDotClick(i)}
            aria-label={`Go to slide ${i + 1}`}
          />
        ))}
      </div>
    </section>
  );
}
